import { useEffect, useState, useMemo, useRef } from 'react';
import { useNavigate } from 'umi';
import moment from 'moment';
import { stringifyUrl } from 'query-string';
import {
    Button,
    Descriptions,
    Form,
    Input,
    message,
    Radio,
    Popconfirm,
    Tabs,
    Tag,
    Tree,
    Spin,
    Tooltip,
    Badge,
    Select,
    Checkbox,
    Empty,
    Steps,
    TimePicker,
    InputNumber,
    Switch
} from 'antd';
import {
    AndroidOutlined,
    AppleOutlined,
    DownOutlined,
    FilterOutlined,
    CloudServerOutlined
} from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { deepcopy } from 'COMMON/components/TreeComponents/Step/utils';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import commonModel from 'COMMON/models/commonModel';
import { CardContent } from 'COMMON/components/common/Card';
import { CardHeader } from 'COMMON/components/common/Card';
import { CardTitle } from 'COMMON/components/common/Card';
import { planTypeOptions } from 'COMMON/utils/planUtils.js';
import NoContent from 'COMMON/components/common/NoContent';
import NodeIcon from 'COMMON/components/NewAddDropdown/components/NodeIcon';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import MemberSelect from 'COMMON/components/Select/MemberSelect';
import RenderTitle from 'FEATURES/front_qe_tools/plan/components/RenderTitle';

import {
    getNewIpRedirect,
    getNewIpRedirectReverse
} from 'COMMON/components/TreeComponents/Step/utils';
import {
    getDevicePoolList,
    getPlanTemplateDetail,
    getPlanTemplateList
} from 'COMMON/api/front_qe_tools/plan/plan';
import {
    queryDailyTaskList,
    createDailyTask,
    updateDailyTask,
    queryDailyTaskDetail
} from 'COMMON/api/front_qe_tools/plan/daily';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getTreeNodeList } from 'COMMON/api/front_qe_tools/tree';

import { getGroupList } from 'COMMON/api/front_qe_tools/group';
import CloudDevice from 'COMMON/components/execution/CloudDevice';
import InstallParams from 'COMMON/components/execution/InstallParams';
import IpRedirect from 'FEATURES/front_qe_tools/plan/components/Setting/IpRedirect';
import RetryTimes from 'FEATURES/front_qe_tools/plan/components/Setting/RetryTimes';
import LocalDevice from 'FEATURES/front_qe_tools/plan/components/Setting/LocalDevice';
import DeviceType from 'FEATURES/front_qe_tools/plan/components/Setting/DeviceType';
import SystemPop from 'FEATURES/front_qe_tools/plan/components/Setting/SystemPop';
import LogCheck from 'FEATURES/front_qe_tools/plan/components/Setting/LogCheck';
import LogCat from 'FEATURES/front_qe_tools/plan/components/Setting/LogCat';
import EnvParams from 'FEATURES/front_qe_tools/plan/components/Setting/EnvParams';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import CaseNodeTree from 'FEATURES/front_qe_tools/plan/components/Setting/CaseNodeTree';
import CreatTemplateModal from 'FEATURES/front_qe_tools/plan/components/CreatTemplateModal';
import styles from './CreateGroupPage.module.less';

const { Search } = Input;

// 扁平树为数组
function flattenTree(node, parentId = null, flattenedTree = []) {
    const nodeCopy = deepcopy(node);
    nodeCopy.parentId = parentId;
    flattenedTree.push(nodeCopy);

    const children = node.children || [];
    for (const child of children) {
        flattenTree(child, node.nodeId, flattenedTree);
    }

    return flattenedTree;
}

// 在树结构中查找指定节点
function findNodeInTree(tree, nodeId) {
    for (const node of tree) {
        if (node.nodeId === nodeId) {
            return node;
        }
        if (node.children && node.children.length > 0) {
            const found = findNodeInTree(node.children, nodeId);
            if (found) {
                return found;
            }
        }
    }
    return null;
}
// 从树结构中提取选中的节点，并生成包含任务的节点ID列表
function getSelectedNodeKeys(tree, treeNodeList, nodeList = []) {
    tree.forEach((item) => {
        if (item.nodeType !== 1) {
            let node = treeNodeList?.find((v) => v.treeNodeId === item.nodeId);
            if (node) {
                node?.taskList?.forEach((task) => {
                    nodeList.push(item.nodeId + '-' + task.osType);
                });
            }
        } else {
            getSelectedNodeKeys(item.children, treeNodeList, nodeList);
        }
    });
    return nodeList;
}

function CreateGroupPage(props) {
    const {
        currentSpace,
        currentModule,
        serverList,
        currentPlanGroup,
        planList,
        setPlanList,
        currentPlan,
        setCurrentPlan
    } = props;
    const query = getQueryParams();
    const [currentStep, setCurrentStep] = useState(0);
    const [currentTemplateId, setCurrentTemplateId] = useState(null);
    const [loading, setLoading] = useState(true);
    const [groupId, setGroupId] = useState(null);
    const [serverShowMore, setServerShowMore] = useState(false);
    const [iosShowMore, setIosShowMore] = useState(false);
    const [androidShowMore, setAndroidShowMore] = useState(false);
    const [createLoading, setCreateLoading] = useState(false);
    const [selectedNode, setSelectedNode] = useState([]);
    const [caseNodeList, setCaseNodeList] = useState([]);
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const [directoryTreeData, setDirectoryTreeData] = useState([]);
    const [nodeTreeMapList, setNodeTreeMapList] = useState([]);
    const [planDetail, setPlanDetail] = useState({});
    const [groupList, setGroupList] = useState([]);
    const [filteredList, setFilteredList] = useState([]);
    const [cloudDeviceList, setCloudDeviceList] = useState({}); // 双端云设备列表
    const [envList, setEnvList] = useState([]); // 环境列表
    const [showOtherConfig, setShowOtherConfig] = useState(false); // 是否显示其他配置
    const [activeExecuteConfigKey, setActiveExecuteConfigKey] = useState('android');
    const [activeAlarmConfigKey, setActiveAlarmConfigKey] = useState('android');
    const [firstStepData, setFirstStepData] = useState({});
    const [secondsStepData, setSecondsStepData] = useState({});
    const caseNodeTreeModalRef = useRef();
    const creatTemplateModalRef = useRef();
    const templateManagerRef = useRef();
    const [createPlanOption, setCreatePlanOption] = useState({
        planName: null,
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                envParams: {}
            }
        }
    });
    const [commonConfigForm] = Form.useForm();
    const [androidConfigForm] = Form.useForm();
    const [serverConfigForm] = Form.useForm();
    const [iosConfigForm] = Form.useForm();
    const [androidAlarmForm] = Form.useForm();
    const [iosAlarmForm] = Form.useForm();
    const [serverAlarmForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    // const [creationMethod, setCreationMethod] = useState('custom');
    const [items, setItems] = useState(executeConfigItems);
    const [creationMethod, setCreationMethod] = useState('custom');
    const [poolList, setPoolList] = useState([]); // 设备池列表
    const [templateList, setTemplateList] = useState([]); // 模板列表
    const navigate = useNavigate();
    const format = 'HH:mm';
    const getNodeList = (nodeTree, nodeList = []) => {
        nodeTree.forEach((item) => {
            if (!item?.children || item.children.length === 0) {
                nodeList.push(item.nodeId);
            }
            if (item?.children && item.children.length > 0) {
                getNodeList(item.children, nodeList);
            }
        });
        return nodeList;
    };
    const executeConfigItems = [
        {
            key: 'android',
            value: '1',
            label: '安卓配置',
            icon: <AndroidOutlined />
        },
        {
            key: 'ios',
            value: '2',
            label: 'iOS 配置',
            icon: <AppleOutlined />
        }
        // {
        //     key: 'server',
        //     value: '4',
        //     label: '服务端配置',
        //     icon: <CloudServerOutlined />
        // }
    ];
    const getNewTree = (tree) => {
        return tree.map((item) => {
            if (item.nodeType === 2) {
                let _osTypeList;
                if (item.signType === 1 && item.osTypeList[0] === 3) {
                    _osTypeList = [3];
                } else if (item.signType === 1 && item.osTypeList.length > 1) {
                    _osTypeList = [item.osTypeList[0]];
                } else if (item.signType === 0 && item.osTypeList[0] === 3) {
                    _osTypeList = [1, 2];
                } else if (item.signType === 0 && item.osTypeList.length > 1) {
                    _osTypeList = item.osTypeList;
                } else {
                    _osTypeList = item.osTypeList;
                }
                item.children = _osTypeList.map((os) => ({
                    nodeId: item.nodeId + '-' + os,
                    os: os,
                    caseRootId: item.caseRootId,
                    caseNodeList: [],
                    signType: item.signType
                }));
                return item;
            } else {
                return { ...item, children: getNewTree(item.children) };
            }
        });
    };
    // 获取模板列表的函数
    const fetchTemplates = async () => {
        // TODO:
        const { templateList } = await getPlanTemplateList({
            moduleId: currentSpace?.id,
            templateType: currentPlanGroup?.planType
        });
        setTemplateList(templateList ?? []);
    };

    // useEffect(() => {
    //     if (creationMethod === 'template') {
    //         fetchTemplates();
    //     }
    // }, [creationMethod]);
    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            // 获取目录树
            let { groupList } = await getGroupList({
                moduleId: currentSpace?.id,
                isArchive: 0
            });
            if (isEmpty(groupList)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setGroupList(groupList);
            let filter = [];
            // 定时巡检测试情况下 展示集成回归用例组
            filter = groupList.filter((group) => +group.groupType === 2);
            setFilteredList(filter);
            setGroupId(filter[0]?.groupId);
            getTreeNodeListWithPlanType(filter[0]?.groupId, groupList);
            // console.log(filteredList, 'filteredList[0]?.groupId');
            // getTreeNodeListWithPlanType(filteredList[0]?.groupId, groupList);
            // 获取设备池列表
            const { poolList } = await getDevicePoolList({
                moduleId: currentSpace?.id,
                poolType: 3 // 设备池类型 1-Android 2-iOS 3-Server
            });
            setPoolList(poolList);
        }
        func();
    }, [currentSpace?.id, query?.planType]);

    const getTreeNodeListWithPlanType = async (groupId, groupList) => {
        try {
            setLoading(true);
            let group;
            //  集成回归用例组
            // if (planType === 1) {
            //     group = groupList.find(item => item?.groupType === 2);
            // }
            // //  本地执行用例组
            // if (planType === 2) {
            //     group = groupList.find(item => item?.groupType === 4);
            // }
            // group = groupList.find(item => item?.groupType === planType);
            if (!groupId) {
                return;
            }
            let { tree } = await getTreeNodeList({ groupId: groupId });
            console.log('定时巡检', tree);
            setDirectoryTreeData(getNewTree(tree));
            setSearchValue('');
            let nodeKeys = tree.map((item) => flattenTree(item)).flat() ?? [];
            setNodeTreeMapList(nodeKeys);
            // 获取 创建第一次进入默认全部key
            setExpandedKeys(nodeKeys.map((item) => item.nodeId));
            setLoading(false);
        } catch (err) {}
    };
    // useEffect(() => {
    //     // console.log(groupList, groupId, 111111);
    //     if (groupList && groupId) {
    //         getTreeNodeListWithPlanType(groupId, groupList);
    //     }
    // }, [groupId, filteredList]);
    // useEffect(() => {
    //     setGroupId(filteredList[0]?.groupId);
    // }, [filteredList]);
    // 获取已创建用例的对应内容
    const getExecutePlanCaseNodeList = (tree, nodeList = []) => {
        tree.map((item) => {
            // 用例
            if (item.nodeType === 2 && item.caseNodeIdList) {
                for (let _item of item.caseNodeList) {
                    nodeList.push({
                        nodeId: item.nodeId,
                        caseRootId: item.caseRootId,
                        osType: _item.osType,
                        caseIdList: _item?.caseNodeIdList ?? []
                    });
                }
            }
            if (!isEmpty(item.children) && item.nodeType === 1) {
                getExecutePlanCaseNodeList(item.children);
            }
        });
        return nodeList;
    };

    useEffect(() => {
        if (query?.planId) {
            let currentPlanDetail = planList?.find((item) => item?.id === +query?.planId);
            let nodeList = getSelectedNodeKeys(
                currentPlanDetail?.nodeTree ?? [],
                currentPlanDetail?.treeNodeList
            );
            setSelectedNode(nodeList);
            let caseList = getExecutePlanCaseNodeList(currentPlanDetail?.nodeTree ?? []);
            setCaseNodeList(caseList);
            setExpandedKeys([
                ...(currentPlanDetail?.nodeTree
                    ? expandedKeys.filter((item) => nodeList.includes(item?.nodeId))
                    : expandedKeys
                )?.map((item) => item?.nodeId)
            ]);
        }
    }, [planList]);

    useEffect(() => {
        if (commonConfigForm && query?.planId) {
            let currentPlanDetail = planList?.find((item) => item?.planId === +query?.planId);
            queryDailyTaskDetail({ planId: +query?.planId }).then((res) => {
                const planDetail = res;
                let nodeList = getSelectedNodeKeys(
                    planDetail?.nodeTree ?? [],
                    planDetail?.treeNodeList
                );
                setSelectedNode(nodeList);
                // 初始化的时候，把原有的数据填入表单
                commonConfigForm.setFieldValue('name', planDetail?.planName ?? '');
                commonConfigForm.setFieldValue('nodeTree', nodeList ?? '');
                commonConfigForm.setFieldValue('cron', planDetail?.cron ?? null);
                const executeType = planDetail?.frequency ? 2 : 1;
                commonConfigForm.setFieldValue('executeType', executeType);
                commonConfigForm.setFieldValue('frequency', planDetail?.frequency ?? null);
                commonConfigForm.setFieldValue(
                    'cloudRetryTimes',
                    planDetail?.cloudRetryTimes ?? null
                );
                if (!isEmpty(planDetail?.timePeriod)) {
                    commonConfigForm.setFieldValue('timePeriod', [
                        moment(planDetail?.timePeriod?.startTime, format),
                        moment(planDetail?.timePeriod?.endTime, format)
                    ]);
                }
                const currentSelectedCaseNodeList = getSelectedCaseNodeList(planDetail);
                setCaseNodeList(currentSelectedCaseNodeList);
                setPlanDetail(planDetail);
                // 遍历 cloudParams 填充对应表单
                const _createPlanOption = createPlanOption;
                planDetail?.cloudParams?.forEach((param) => {
                    if (param.type === 1) {
                        // Android
                        const _param = {
                            retryTimes: param?.retryTimes,
                            deviceType: 1,
                            localDevice: param?.deviceIdList,
                            cloudDevice: param?.poolList,
                            sysAlertClear: param?.sysAlertClear,
                            logCollect: param?.logCollect?.needLog,
                            filter: param?.logCollect?.filter,
                            logcheckInfo: param?.logcheckInfo?.needLogCheck,
                            cuid: param?.logcheckInfo?.cuid,
                            installParams: param?.installParams,
                            envParams: param?.envParams
                        };
                        androidConfigForm.setFieldsValue(_param);
                        _createPlanOption.executeConfig.android = _param;
                        androidAlarmForm.setFieldsValue({
                            toid: param?.alarmInfo?.toid,
                            webhook: param?.alarmInfo?.webhook,
                            statusList: param?.alarmInfo?.statusList,
                            atuseridName: param?.alarmInfo?.atuseridName
                        });
                    } else if (param.type === 2) {
                        // iOS
                        const _param = {
                            retryTimes: param?.retryTimes,
                            deviceType: 1,
                            localDevice: param?.deviceIdList,
                            cloudDevice: param?.poolList,
                            sysAlertClear: param?.sysAlertClear,
                            logCollect: param?.logCollect?.needLog,
                            filter: param?.logCollect?.filter,
                            logcheckInfo: param.logcheckInfo?.needLogCheck,
                            cuid: param?.logcheckInfo?.cuid,
                            installParams: param?.installParams,
                            envParams: param?.envParams
                        };
                        iosConfigForm.setFieldsValue(_param);
                        _createPlanOption.executeConfig.ios = _param;
                        iosAlarmForm.setFieldsValue({
                            toid: param?.alarmInfo?.toid,
                            webhook: param?.alarmInfo?.webhook,
                            statusList: param?.alarmInfo?.statusList,
                            atuseridName: param?.alarmInfo?.atuseridName
                        });
                    } else if (param.type === 4) {
                        // Server
                        const _param = {
                            poolId: param?.poolList[0],
                            deviceType: 1,
                            envParams: param?.envParams,
                            modeType: param?.modeType,
                            stableEnvironmentId:
                                param?.modeConfig?.serverConfig?.stableEnvironmentId,
                            testEnvironmentId: param?.modeConfig?.serverConfig?.testEnvironmentId,
                            jsonSchemaCheck: param?.modeConfig?.assertConfig?.jsonSchemaCheck,
                            intelligentNoiseReduce:
                                param?.modeConfig?.assertConfig?.intelligentNoiseReduce
                        };
                        serverConfigForm.setFieldsValue(_param);
                        _createPlanOption.executeConfig.server = _param;
                        serverAlarmForm.setFieldsValue({
                            toid: param?.alarmInfo?.toid,
                            webhook: param?.alarmInfo?.webhook,
                            statusList: param?.alarmInfo?.statusList,
                            atuseridName: param?.alarmInfo?.atuseridName
                        });
                    }
                });
                setCreatePlanOption(_createPlanOption);
            });
        } else {
            commonConfigForm.setFieldValue('executeType', 1);
        }
    }, [commonConfigForm, androidConfigForm, iosConfigForm, serverConfigForm, planList]);

    useEffect(() => {
        if (androidConfigForm) {
            androidConfigForm.setFieldValue('deviceType', 2);
            androidConfigForm.setFieldValue('mockSwitch', true);
        }
    }, [androidConfigForm]);

    useEffect(() => {
        if (iosConfigForm) {
            iosConfigForm.setFieldValue('deviceType', 2);
            iosConfigForm.setFieldValue('mockSwitch', true);
        }
    }, [iosConfigForm]);
    useEffect(() => {
        obtainCloudDeviceList();
        obtaionEnvParamsList();
    }, []);

    const onClick = async () => {
        try {
            await androidAlarmForm?.validateFields();
            await iosAlarmForm?.validateFields();
            await serverAlarmForm?.validateFields();
        } catch (e) {
            messageApi.warning('请填写完整表单');
            return;
        }
        try {
            // const commonConfigFormValues = commonConfigForm.getFieldsValue();
            let newNodeTree =
                firstStepData?.nodeTree?.filter((item) => ('' + item).includes('-')) || [];
            let treeNodeIdList = getTreeNodeIdList(
                directoryTreeData,
                [...new Set(newNodeTree?.map((item) => +(item + '').split('-')[0]))],
                newNodeTree
            );

            // 验证用户是否已经进行了筛选操作
            let hasUnfilteredNodes = false;
            let unfilteredNodeNames = [];

            for (let item of treeNodeIdList) {
                let caseDetail = caseNodeList?.find(
                    (it) => it?.nodeId === item?.treeNodeId && it.osType === item?.osType
                );
                if (!caseDetail || !caseDetail.caseIdList || caseDetail.caseIdList.length === 0) {
                    hasUnfilteredNodes = true;
                    // 获取节点名称用于提示
                    const nodeInfo = findNodeInTree(directoryTreeData, item?.treeNodeId);
                    if (nodeInfo) {
                        unfilteredNodeNames.push(nodeInfo.title);
                    }
                }
            }

            if (hasUnfilteredNodes) {
                messageApi.warning(
                    `请点击筛选按钮，为以下节点选择具体的测试用例：${unfilteredNodeNames.join('、')}`
                );
                return;
            }

            let planId;
            for (let item of treeNodeIdList) {
                let list = caseNodeList?.find(
                    (it) => it?.nodeId === item?.treeNodeId && it.osType === item?.osType
                )?.caseIdList;
                if (list) {
                    item.caseNodeList = list;
                }
            }
            // return;
            setCreateLoading(true);
            // if (query?.planId) {
            // if (+query?.planType === 4) {
            //     await ({
            //         planId: query?.planId,
            //         name: firstStepData?.name,
            //         treeNodeIdList: treeNodeIdList,
            //         cron: firstStepData?.cron
            //     });
            // } else {

            // }

            // } else if (!query?.planId) {
            let params = {
                moduleId: currentSpace?.id,
                name: firstStepData.name,
                treeNodeIdList: treeNodeIdList
            };
            // const androidConfigFormValues = androidConfigForm.getFieldsValue();
            // const iosConfigFormValues = iosConfigForm.getFieldsValue();
            // const serverConfigFormValues = serverConfigForm.getFieldsValue();
            const androidConfigFormValues = secondsStepData?.android;
            const iosConfigFormValues = secondsStepData?.ios;
            const serverConfigFormValues = secondsStepData?.server;
            const androidAlarmFormValues = androidAlarmForm.getFieldsValue();
            const iosAlarmFormValues = iosAlarmForm.getFieldsValue();
            const serverAlarmFormValues = serverAlarmForm.getFieldsValue();
            let cloudParams = [];
            // 若有自动化配置
            if (androidConfigFormValues?.deviceType !== 2) {
                console.log('createPlanOption', createPlanOption);
                let androidParmas = {
                    type: 1,
                    retryTimes: androidConfigFormValues?.retryTimes ?? 0,

                    sysAlertClear: androidConfigFormValues?.sysAlertClear ?? true,
                    logCollect: {
                        needLog: androidConfigFormValues?.logCollect ?? false,
                        filter: androidConfigFormValues?.logCollect
                            ? androidConfigFormValues?.filter
                            : ''
                    },
                    requestDirectMap: getNewIpRedirect(
                        createPlanOption?.executeConfig || [],
                        'android'
                    ).filter((item) => {
                        return (
                            item.oriAddress.hostname !== undefined &&
                            item.targetAddress.hostname !== undefined
                        );
                    }),
                    envParams: {
                        envDetail: {
                            envId: createPlanOption?.executeConfig?.android?.envParams?.envId,
                            paramList:
                                createPlanOption?.executeConfig?.android?.envParams?.paramList?.map(
                                    (item) => ({
                                        paramKey: item.paramKey,
                                        envValue: item.envValue
                                    })
                                ),
                            appList:
                                createPlanOption?.executeConfig?.android?.envParams?.appList?.map(
                                    (item) => ({
                                        appId: item.appId,
                                        envValue: item.envValue
                                    })
                                ),
                            serverList:
                                createPlanOption?.executeConfig?.android?.envParams?.serverList?.map(
                                    (item) => ({
                                        serverId: item.serverId,
                                        envValue: item.envValue
                                    })
                                )
                        }
                    },
                    alarmInfo: {
                        toid: androidAlarmFormValues?.toid ? +androidAlarmFormValues?.toid : null, // 群号
                        webhook: androidAlarmFormValues?.webhook, // 机器人地址
                        atuseridName: androidAlarmFormValues?.atuseridName, // 需@的人
                        statusList: androidAlarmFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                    }
                };
                // 仅本地设备参数 打点自动化校验
                if (androidConfigFormValues?.deviceType === 3) {
                    androidParmas.deviceIdList = androidConfigFormValues?.localDevice;
                    androidParmas.poolList = [0];
                    androidParmas.logcheckInfo = {
                        needLogCheck: androidConfigFormValues?.logcheckInfo ?? false,
                        cuid: androidConfigFormValues?.logcheckInfo
                            ? androidConfigFormValues?.cuid
                            : ''
                    };
                }
                // 仅云端设备参数 安装app
                if (androidConfigFormValues?.deviceType === 1) {
                    androidParmas.poolList = androidConfigFormValues?.cloudDevice;
                    // name为空时，不传
                    androidParmas.installParams = (
                        androidConfigFormValues?.installParams ?? []
                    ).map((item) => {
                        const { name, ...rest } = item;
                        return name ? { name, ...rest } : { ...rest };
                    });
                }
                cloudParams.push(androidParmas);
            }
            if (iosConfigFormValues?.deviceType !== 2) {
                let iosParmas = {
                    type: 2,
                    retryTimes: iosConfigFormValues?.retryTimes ?? 0,
                    sysAlertClear: iosConfigFormValues?.sysAlertClear ?? true,
                    requestDirectMap: getNewIpRedirect(
                        createPlanOption?.executeConfig || [],
                        'ios'
                    ).filter((item) => {
                        return (
                            item.oriAddress.hostname !== undefined &&
                            item.targetAddress.hostname !== undefined
                        );
                    }),
                    envParams: {
                        envDetail: {
                            envId: createPlanOption?.executeConfig?.ios?.envParams?.envId,
                            paramList:
                                createPlanOption?.executeConfig?.ios?.envParams?.paramList?.map(
                                    (item) => ({
                                        paramKey: item.paramKey,
                                        envValue: item.envValue
                                    })
                                ),
                            appList: createPlanOption?.executeConfig?.ios?.envParams?.appList?.map(
                                (item) => ({
                                    appId: item.appId,
                                    envValue: item.envValue
                                })
                            ),
                            serverList:
                                createPlanOption?.executeConfig?.ios?.envParams?.serverList?.map(
                                    (item) => ({
                                        serverId: item.serverId,
                                        envValue: item.envValue
                                    })
                                )
                        }
                    },
                    alarmInfo: {
                        toid: iosAlarmFormValues?.toid ? +iosAlarmFormValues?.toid : null, // 群号
                        webhook: iosAlarmFormValues?.webhook, // 机器人地址
                        atuseridName: iosAlarmFormValues?.atuseridName, // 需@的人
                        statusList: iosAlarmFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                    }
                };
                // 仅本地设备参数 打点自动化校验
                if (iosConfigFormValues?.deviceType === 3) {
                    iosParmas.deviceIdList = iosConfigFormValues?.localDevice;
                    iosParmas.poolList = [0];
                    iosParmas.logcheckInfo = {
                        needLogCheck: iosConfigFormValues?.logcheckInfo ?? false,
                        cuid: iosConfigFormValues?.logcheckInfo ? iosConfigFormValues?.cuid : ''
                    };
                    if (iosConfigFormValues?.localDevice?.length === 0) {
                        messageApi.error('设备获取失败，请重新选择');
                        return;
                    }
                }
                // 仅云端设备参数 安装app
                if (iosConfigFormValues?.deviceType === 1) {
                    iosParmas.poolList = iosConfigFormValues.cloudDevice;
                    // name 字段为空时，不传
                    iosParmas.installParams = (iosConfigFormValues?.installParams ?? []).map(
                        (item) => {
                            const { name, ...rest } = item;
                            return name ? { name, ...rest } : { ...rest };
                        }
                    );
                    if (iosConfigFormValues.cloudDevice.length === 0) {
                        messageApi.error('设备池获取失败，请重新选择');
                        return;
                    }
                }
                cloudParams.push(iosParmas);
            }
            // server配置
            if (serverConfigFormValues?.deviceType === 1) {
                let serverParmas = {
                    type: 4,
                    modeType: serverConfigFormValues?.modeType ?? 0,
                    modeConfig: {
                        // 模式配置:
                        serverConfig: {
                            //  Server 配置
                            stableEnvironmentId: serverConfigFormValues?.stableEnvironmentId, // 稳定版环境id
                            testEnvironmentId: serverConfigFormValues?.testEnvironmentId // 待测版环境id
                        },
                        assertConfig: {
                            // 断言配置
                            jsonSchemaCheck: serverConfigFormValues?.jsonSchemaCheck, // JSON Schema 校验
                            intelligentNoiseReduce: serverConfigFormValues?.intelligentNoiseReduce // 智能去噪
                        }
                    },
                    poolList: [serverConfigFormValues?.poolId],
                    envParams: {
                        envId: createPlanOption?.executeConfig?.server?.envParams?.envId,
                        envDetail: {
                            envId: createPlanOption?.executeConfig?.server?.envParams?.envId,
                            paramList:
                                createPlanOption?.executeConfig?.server?.envParams?.paramList?.map(
                                    (item) => ({
                                        paramKey: item.paramKey,
                                        envValue: item.envValue
                                    })
                                ),
                            appList:
                                createPlanOption?.executeConfig?.server?.envParams?.appList?.map(
                                    (item) => ({
                                        appId: item.appId,
                                        envValue: item.envValue
                                    })
                                ),
                            serverList:
                                createPlanOption?.executeConfig?.server?.envParams?.serverList?.map(
                                    (item) => ({
                                        serverId: item.serverId,
                                        envValue: item.envValue
                                    })
                                )
                        }
                    },
                    // 报警
                    alarmInfo: {
                        toid: serverAlarmFormValues?.toid ? +serverAlarmFormValues?.toid : null, // 群号
                        webhook: serverAlarmFormValues?.webhook, // 机器人地址
                        atuseridName: serverAlarmFormValues?.atuseridName, // 需@的人
                        statusList: serverAlarmFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                    }
                };
                cloudParams.push(serverParmas);
            }
            // cloudParams.push(serverConfigFormValues);
            if (!isEmpty(cloudParams)) {
                params.cloudParams = cloudParams;
            }
            // cron
            if (firstStepData.cron) {
                params.cron = firstStepData.cron;
            }
            // 时间段
            if (!isEmpty(firstStepData?.timePeriod)) {
                params.timePeriod = {
                    startTime: firstStepData?.timePeriod?.[0].format('HH:mm'),
                    endTime: firstStepData?.timePeriod?.[1].format('HH:mm')
                };
            }
            if (firstStepData.frequency) {
                params.frequency = firstStepData.frequency;
            }
            params.cloudRetryTimes = firstStepData.cloudRetryTimes;
            if (query?.planId) {
                await updateDailyTask({
                    ...params,
                    planId: query?.planId
                });
                planId = query?.planId;
            } else {
                const res = await createDailyTask(params);
                planId = res?.planId;
            }
            let { planList } = await queryDailyTaskList({
                moduleId: currentSpace?.id
                // planType: currentPlanGroup?.planType
            });

            let planInfo = await queryDailyTaskDetail({ planId: +planId });
            setPlanList(
                planList?.map((item) => {
                    if (item?.planId === +planId) {
                        return {
                            ...planInfo
                        };
                    } else {
                        return item;
                    }
                })
            );
            setCurrentPlan(planInfo);
            setCreateLoading(false);
            navigate(
                stringifyUrl({
                    url: '/' + currentModule + '/daily/index',
                    query: {
                        moduleId: currentSpace?.id,
                        // planType: query?.planType ?? 1,
                        planId: planId,
                        'filters[planId]': query?.['filters[planId]']
                    }
                })
            );
        } catch (err) {
            console.log(err?.message ?? err, '创建计划失败');
            setCreateLoading(false);
        }
    };

    const onExpand = (newExpandedKeys) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };

    //递归过滤隐藏菜单
    const getTreeNodeIdList = (tree = [], nodeList, nodeListWithOs, arr = []) => {
        if (!tree.length) {
            return [];
        }
        for (let item of tree) {
            // 若无子节点且不在list中，则不显示
            if (item.nodeType === 1) {
                getTreeNodeIdList(item?.children, nodeList, nodeListWithOs, arr);
            } else if (!nodeList.includes(item.nodeId)) {
                continue;
            } else if (nodeList.includes(item.nodeId)) {
                [1, 2, 3, 4, 5, 6].forEach((os) => {
                    if (nodeListWithOs.includes(item.nodeId + '-' + os)) {
                        arr.push({
                            treeNodeId: item?.nodeId,
                            osType: os
                        });
                    }
                });
            }
        }
        return arr;
    };

    // 获取云端设备池列表
    const obtainCloudDeviceList = async () => {
        if (!currentSpace?.id) {
            return;
        }
        let androidRes = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: 1
        });
        let iosRes = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: 2
        });
        setCloudDeviceList({
            android: androidRes?.poolList,
            ios: iosRes?.poolList
        });
    };

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        if (!currentSpace?.id) {
            return;
        }
        let androidRes = await getEnvList({
            moduleId: currentSpace?.id,
            osType: 1
        });
        let iosRes = await getEnvList({
            moduleId: currentSpace?.id,
            osType: 2
        });
        let serverRes = await getEnvList({
            moduleId: currentSpace?.id,
            osType: 4
        });
        setEnvList({
            android: androidRes.envList,
            ios: iosRes.envList,
            server: serverRes?.envList
        });
    };

    const getSelectedCaseNodeList = (planDetail) => {
        const caseNodeList = [];
        const getNodeList = (nodeList) => {
            nodeList?.forEach((item) => {
                // nodeType 为2的节点
                if (item?.nodeType === 2) {
                    // 遍历 caseNodeList 选中的节点
                    item?.caseNodeList?.forEach((node) => {
                        caseNodeList.push({
                            nodeId: item.nodeId,
                            osType: node?.osType,
                            caseIdList: node.caseNodeIdList,
                            caseRootId: item.caseRootId
                        });
                    });
                } else {
                    // 目录
                    getNodeList(item.children);
                }
            });
        };
        getNodeList(planDetail?.nodeTree);
        return caseNodeList;
    };
    // 递归过滤隐藏菜单
    const treeData = useMemo(() => {
        const loop = (data) => {
            let newData = [];
            data.forEach((item) => {
                const strTitle = !item?.nodeName || item?.nodeName === null ? '' : item.nodeName;
                const index = strTitle.indexOf(searchValue);
                const beforeStr = strTitle.substring(0, index);
                const afterStr = strTitle.slice(index + searchValue.length);
                const title =
                    index > -1 ? (
                        <span>
                            {beforeStr}
                            <span className={styles.highlight}>{searchValue}</span>
                            {afterStr}
                        </span>
                    ) : (
                        <span>{strTitle}</span>
                    );
                if (item?.nodeType === 1) {
                    newData.push({
                        title: (
                            <>
                                <NodeIcon type={item.nodeType} />
                                {title}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId,
                        children: loop(item.children)
                    });
                } else if (item?.nodeType === 2) {
                    newData.push({
                        title: (
                            <>
                                <NodeIcon type={item.nodeType} />
                                {title}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId,
                        children: loop(item.children)
                    });
                } else if (item.os) {
                    let curNodeList =
                        caseNodeList?.find(
                            (list) => list?.nodeId === item?.nodeId && item?.os === list?.osType
                        )?.caseIdList ?? [];
                    newData.push({
                        title: (
                            <>
                                <RenderTitle os={item.os} />
                                {selectedNode.includes(item.nodeId) && (
                                    <Tooltip title="筛选需要创建的用例, 默认全选" placement="right">
                                        <Badge dot={curNodeList?.length > 0}>
                                            <FilterOutlined
                                                className={styles.filter}
                                                onClick={() => {
                                                    caseNodeTreeModalRef?.current?.show(
                                                        item.caseRootId,
                                                        item.os,
                                                        +item.nodeId.split('-')?.[0],
                                                        caseNodeList
                                                    );
                                                }}
                                            />
                                        </Badge>
                                    </Tooltip>
                                )}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId
                    });
                }
            });
            return newData;
        };
        console.log('定时巡检11', directoryTreeData);
        return loop(directoryTreeData);
    }, [searchValue, directoryTreeData, selectedNode, caseNodeList]);

    const onSearchValueChange = (e) => {
        const { value } = e.target;
        const newExpandedKeys = nodeTreeMapList
            .map((item) => {
                if (`${item?.nodeName || ''}`.includes(value)) {
                    return item?.nodeId || null;
                }
                return null;
            })
            .filter((item) => item !== null);
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(true);
        setSearchValue(value);
    };
    // console.log(treeData);

    // const planTypeOptions = [
    //     {
    //         value: 1,
    //         label: '源于集成回归用例组'
    //     },
    //     {
    //         value: 2,
    //         disabled: +query?.planType === 5,
    //         label: '源于本地执行用例组'
    //     }
    // ];


    const AlarmConfigForm = ({ activeKey, form }) => {
        const [showMore, setShowMore] = useState(false);
        return (
            <Form
                style={{ display: activeAlarmConfigKey !== activeKey ? 'none' : 'block' }}
                form={form}
                layout="vertical"
                requiredMark={false}
                colon={false}
                initialValues={{
                    statusList: [3, 4]
                }}
            >
                <CardTitle text="如流群配置" style={{ marginTop: 0 }} />
                <Form.Item label="如流群" name="toid">
                    <Input
                        placeholder="请输入如流群"
                        allowClear
                        onChange={(e) => {
                            if (isEmpty(e.target.value)) {
                                setShowMore(false);
                            } else {
                                setShowMore(true);
                            }
                        }}
                    />
                </Form.Item>
                {/* {showMore && ( */}
                <Form.Item noStyle shouldUpdate>
                    {({ getFieldValue }) => {
                        const toidValue = getFieldValue('toid');
                        return (
                            <>
                                {toidValue || toidValue === 0 ? (
                                    <div>
                                        <Form.Item label="报警机器人地址" name="webhook">
                                            <Input placeholder="请输入报警机器人地址" allowClear />
                                        </Form.Item>
                                        <Form.Item label="@ 的人" name="atuseridName">
                                            <MemberSelect
                                                mode="multiple"
                                                variant="outlined"
                                                placeholder="请选择 @ 的人邮箱前缀"
                                            />
                                        </Form.Item>
                                        <CardTitle text="用例报警配置" style={{ marginTop: 0 }} />
                                        <Form.Item label="报警类型" name="statusList">
                                            <Checkbox.Group>
                                                <Checkbox value={2}>成功</Checkbox>
                                                <Checkbox value={3}>失败</Checkbox>
                                                <Checkbox value={4}>异常</Checkbox>
                                            </Checkbox.Group>
                                        </Form.Item>
                                    </div>
                                ) : null}
                            </>
                        );
                    }}
                </Form.Item>

                {/* )} */}
            </Form>
        );
    };

    const steps = [
        {
            title: '任务信息',
            content: (
                <div>
                    <Form
                        form={commonConfigForm}
                        layout="vertical"
                        requiredMark={false}
                        colon={false}
                    >
                        <CardTitle text="任务名称" />
                        <Form.Item
                            name="name"
                            rules={[
                                {
                                    required: true,
                                    message: '请输入任务名称'
                                }
                            ]}
                        >
                            <Input placeholder="请输入内容" allowClear />
                        </Form.Item>
                        <CardTitle text="执行配置" />
                        <Form.Item
                            label="执行时间"
                            name="executeType"
                            labelCol={{ span: 8 }}
                            wrapperCol={{ span: 16 }}
                        >
                            <Radio.Group>
                                <Radio value={1}>Cron 表达式</Radio>
                                <Radio value={2}>自定义</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) =>
                                prevValues.executeType !== currentValues.executeType
                            }
                        >
                            {({ getFieldValue }) => {
                                return (
                                    <>
                                        {getFieldValue('executeType') === 1 && (
                                            <>
                                                <div>
                                                    <Form.Item
                                                        name="cron"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message: '请输入 Cron 表达式'
                                                            }
                                                        ]}
                                                        label="Cron 表达式"
                                                    >
                                                        <Input
                                                            placeholder="请输入 Cron 表达式"
                                                            allowClear
                                                        />
                                                    </Form.Item>
                                                </div>
                                            </>
                                        )}
                                        {getFieldValue('executeType') === 2 && (
                                            <>
                                                <div>
                                                    <Form.Item
                                                        name="frequency"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    '请输入执行频率 (单位: 分钟)'
                                                            }
                                                        ]}
                                                        label="执行频率"
                                                        tooltip="计划的执行频率，最小值：120"
                                                    >
                                                        <InputNumber
                                                            style={{ width: '100%' }}
                                                            placeholder="请输入执行频率 (单位: 分钟)"
                                                            allowClear
                                                            min={120}
                                                            addonAfter={'分钟'}
                                                        />
                                                    </Form.Item>
                                                </div>
                                            </>
                                        )}
                                        <Form.Item
                                            name="timePeriod"
                                            // rules={[
                                            //     {
                                            //         required: true,
                                            //         message: '请选择执行时间段'
                                            //     }
                                            // ]}
                                            label="执行时间段"
                                        >
                                            <TimePicker.RangePicker
                                                format={format}
                                                style={{ width: '100%' }}
                                                placeholder={['开始时间', '结束时间']}
                                            />
                                        </Form.Item>
                                    </>
                                );
                            }}
                        </Form.Item>
                        <div>{/* <CardTitle text="Cron 表达式" /> */}</div>
                        <Form.Item
                            name="cloudRetryTimes"
                            label="重试次数"
                            rules={[
                                {
                                    required: true,
                                    message: ''
                                }
                            ]}
                            initialValue={1}
                            tooltip="单计划执行失败后自动重试的最大次数，范围：0-3"
                        >
                            <InputNumber
                                placeholder="请输入重试次数"
                                min={0}
                                max={3}
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                        <CardTitle text="测试用例">
                            <Select
                                variant="borderless"
                                style={{
                                    width: +query?.planType !== 5 ? 161 : 145
                                }}
                                className={styles.selecPlanType}
                                popupMatchSelectWidth={false}
                                size="small"
                                value={groupId}
                                suffixIcon={+query?.planType !== 5 ? <DownOutlined /> : null}
                                options={planTypeOptions(filteredList)}
                                onChange={(value) => {
                                    // setPlanType(value);
                                    setGroupId(value);
                                    getTreeNodeListWithPlanType(value, groupList);
                                    // 本地要切换设备
                                    if (query?.planType === 2) {
                                        if (androidConfigForm.getFieldValue('deviceType') === 1) {
                                            androidConfigForm.setFieldValue(
                                                'deviceType',
                                                isElectron() ? 3 : 2
                                            );
                                        }
                                        if (iosConfigForm.getFieldValue('deviceType') === 1) {
                                            iosConfigForm.setFieldValue(
                                                'deviceType',
                                                isElectron() ? 3 : 2
                                            );
                                        }
                                    }
                                }}
                            />
                            {/* 如果是更新配置，不支持从模板导入 */}
                            {!query?.planId && (
                                <Popconfirm
                                    placement="right"
                                    title={'该操作会覆盖当前已选择的数据，请谨慎操作'}
                                    // width={300}
                                    // disabled
                                    destroyTooltipOnHide
                                    // zIndexPopup={1}
                                    style={{ width: 800 }}
                                    description={
                                        <SelectWithDropdownRender
                                            style={{
                                                flex: 1,
                                                margin: '10px 0',
                                                width: 300
                                            }}
                                            placeholder="请选择模板" // 占位符文本
                                            text="计划模板选择" // 用于显示的文本
                                            disabled={query?.planId} // 根据需要启用或禁用选择
                                            showSearchIcon={false} // 显示搜索图标
                                            // 选项列表 只展示当前planType下用例组对应的模板
                                            options={templateList
                                                .filter((item) =>
                                                    filteredList.some(
                                                        (filterItem) =>
                                                            filterItem.groupId === item.groupId
                                                    )
                                                )
                                                .map((item) => ({
                                                    value: item.templateId,
                                                    label: item.templateName
                                                }))}
                                            filterOption={(input, option) =>
                                                option.label.includes(input)
                                            }
                                            showSearch
                                            value={currentTemplateId} // 当前选中的服务ID
                                            onChange={(value) => {
                                                // 当选中的服务变化时，执行的操作
                                                setCurrentTemplateId(value);
                                            }}
                                            onClick={() => {
                                                fetchTemplates();
                                            }}
                                            addChange={() => {
                                                creatTemplateModalRef.current.show();
                                            }}
                                            settingChange={() => {
                                                templateManagerRef.current?.show({
                                                    key: 'plan-template',
                                                    label: '计划模板',
                                                    path: '/system-setting/plan-template'
                                                });
                                            }}
                                            allowClear
                                        />
                                    }
                                    okText="确定"
                                    cancelText="取消"
                                    onConfirm={async () => {
                                        try {
                                            // 清空当前的表单数据;
                                            commonConfigForm.resetFields();
                                            androidConfigForm.resetFields();
                                            iosConfigForm.resetFields();
                                            serverConfigForm.resetFields();
                                            const currentTemplateDetail =
                                                await getPlanTemplateDetail({
                                                    templateId: currentTemplateId
                                                });
                                            setGroupId(currentTemplateDetail?.groupId);
                                            getTreeNodeListWithPlanType(
                                                currentTemplateDetail?.groupId,
                                                groupList
                                            );
                                            const templateConfig =
                                                currentTemplateDetail.templateConfig;
                                            const selectedNodes = templateConfig.treeNodeIdList.map(
                                                (node) => `${node.treeNodeId}-${node.osType}`
                                            );
                                            // 设置计划名称
                                            commonConfigForm.setFieldsValue({
                                                name: templateConfig.name,
                                                nodeTree: selectedNodes,
                                                executeType: 1
                                            });
                                            setSelectedNode(selectedNodes);

                                            // 设置自动化配置
                                            templateConfig?.cloudParams?.forEach((param) => {
                                                if (param.type === 1) {
                                                    // Android
                                                    androidConfigForm.setFieldsValue({
                                                        retryTimes: param?.retryTimes,
                                                        localDevice: param?.deviceIdList,
                                                        cloudDevice: param?.poolList,
                                                        sysAlertClear: param?.sysAlertClear,
                                                        logCollect: param?.logCollect?.needLog,
                                                        filter: param?.logCollect?.filter,
                                                        logcheckInfo:
                                                            param?.logcheckInfo?.needLogCheck,
                                                        cuid: param?.logcheckInfo?.cuid,
                                                        installParams: param?.installParams,

                                                        deviceType:
                                                            param?.deviceIdList?.length > 0
                                                                ? 3
                                                                : param?.poolList?.length > 0
                                                                ? 1
                                                                : 2
                                                    });
                                                    androidAlarmForm.setFieldsValue({
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        atuseridName: param?.alarmInfo?.atuseridName
                                                    });
                                                    if (param?.alarmInfo?.toid) {
                                                        setAndroidShowMore(true);
                                                    } else {
                                                        setAndroidShowMore(false);
                                                    }
                                                } else if (param.type === 2) {
                                                    // iOS
                                                    iosConfigForm.setFieldsValue({
                                                        retryTimes: param.retryTimes,
                                                        deviceType:
                                                            param?.deviceIdList?.length > 0
                                                                ? 3
                                                                : param?.poolList?.length > 0
                                                                ? 1
                                                                : 2,
                                                        localDevice: param?.deviceIdList,
                                                        cloudDevice: param?.poolList,
                                                        sysAlertClear: param?.sysAlertClear,
                                                        logCollect: param?.logCollect?.needLog,
                                                        filter: param?.logCollect?.filter,
                                                        logcheckInfo:
                                                            param?.logcheckInfo?.needLogCheck,
                                                        cuid: param?.logcheckInfo?.cuid,
                                                        installParams: param?.installParams,
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        atuseridName: param?.alarmInfo?.atuseridName
                                                    });
                                                    iosAlarmForm.setFieldsValue({
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        atuseridName: param?.alarmInfo?.atuseridName
                                                    });
                                                    if (param?.alarmInfo?.toid) {
                                                        setIosShowMore(true);
                                                    } else {
                                                        setIosShowMore(false);
                                                    }
                                                } else if (param.type === 4) {
                                                    // Server
                                                    serverConfigForm.setFieldsValue({
                                                        poolId: param?.poolList[0], // 只取第一个执行器集群
                                                        envParams: param?.envParams, // 根据需要调整结构
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        deviceType:
                                                            param?.poolList?.length > 0 ? 1 : 2,
                                                        atuseridName:
                                                            param?.alarmInfo?.atuseridName,
                                                        modeType: param?.modeType,
                                                        stableEnvironmentId:
                                                            param?.modeConfig?.serverConfig
                                                                ?.stableEnvironmentId,
                                                        testEnvironmentId:
                                                            param?.modeConfig?.serverConfig
                                                                ?.testEnvironmentId,
                                                        jsonSchemaCheck:
                                                            param?.modeConfig?.assertConfig
                                                                ?.jsonSchemaCheck,
                                                        intelligentNoiseReduce:
                                                            param?.modeConfig?.assertConfig
                                                                ?.intelligentNoiseReduce
                                                    });
                                                    serverAlarmForm.setFieldsValue({
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        atuseridName: param?.alarmInfo?.atuseridName
                                                    });
                                                    if (param?.alarmInfo?.toid) {
                                                        setServerShowMore(true);
                                                    } else {
                                                        setServerShowMore(false);
                                                    }
                                                }
                                            });
                                            // 更新状态
                                            setCreatePlanOption((prev) => ({
                                                ...prev,
                                                executeConfig: {
                                                    android: {
                                                        ...prev.executeConfig?.android,
                                                        envParams:
                                                            templateConfig?.cloudParams?.find(
                                                                (p) => p.type === 1
                                                            )?.envParams || {},
                                                        ipRedirect:
                                                            getNewIpRedirectReverse(
                                                                templateConfig?.cloudParams?.find(
                                                                    (p) => p.type === 1
                                                                )?.requestDirectMap
                                                            ) || []
                                                    },
                                                    ios: {
                                                        ...prev.executeConfig.ios,
                                                        envParams:
                                                            templateConfig?.cloudParams?.find(
                                                                (p) => p.type === 2
                                                            )?.envParams || {},
                                                        ipRedirect:
                                                            getNewIpRedirectReverse(
                                                                templateConfig?.cloudParams?.find(
                                                                    (p) => p.type === 2
                                                                )?.requestDirectMap
                                                            ) || []
                                                    },
                                                    server: {
                                                        ...prev.executeConfig.server,
                                                        envParams:
                                                            templateConfig?.cloudParams?.find(
                                                                (p) => p.type === 4
                                                            )?.envParams || {}
                                                    }
                                                }
                                            }));
                                            setCaseNodeList(
                                                templateConfig?.treeNodeIdList?.map((item) => {
                                                    return {
                                                        ...item,
                                                        nodeId: item.treeNodeId,
                                                        caseIdList: item.caseNodeList
                                                    };
                                                })
                                            );
                                        } catch (error) {
                                            console.error(error);
                                            messageApi.error('加载模板失败，请重试');
                                        }
                                    }}
                                >
                                    <Button type="link">从模板快速导入配置</Button>
                                </Popconfirm>
                            )}
                        </CardTitle>
                        <div className={styles.cardLayout}>
                            <Form.Item
                                name="nodeTree"
                                shouldUpdate={() => false}
                                required
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (selectedNode && selectedNode.length > 0) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject(
                                                new Error('请选择需要测试的用例')
                                            );
                                        }
                                    })
                                ]}
                            >
                                <div className={styles.nodeTree}>
                                    <Search
                                        className={styles.search}
                                        value={searchValue}
                                        placeholder="请输入要检索的内容"
                                        onChange={(e) => onSearchValueChange(e)}
                                    />
                                    <Spin spinning={loading}>
                                        <div
                                            className={styles.tree}
                                            style={{ maxHeight: window.innerHeight - 400 }}
                                        >
                                            {treeData?.length ? (
                                                <Tree
                                                    onExpand={onExpand}
                                                    checkable
                                                    showLine
                                                    autoExpandParent={autoExpandParent}
                                                    expandedKeys={expandedKeys}
                                                    treeData={treeData}
                                                    checkedKeys={selectedNode}
                                                    onCheck={(keys, { checkedNodes }) => {
                                                        setSelectedNode([
                                                            ...checkedNodes.map((item) => item.key)
                                                        ]);
                                                        let newCaseNodeList = [];
                                                        keys = keys.filter((item) =>
                                                            (item + '').includes('-')
                                                        );
                                                        for (let item of caseNodeList) {
                                                            if (
                                                                keys.includes(
                                                                    item.nodeId + '-' + item.osType
                                                                )
                                                            ) {
                                                                newCaseNodeList.push(item);
                                                            }
                                                        }
                                                        setCaseNodeList(newCaseNodeList);
                                                        commonConfigForm.setFieldValue('nodeTree', [
                                                            ...checkedNodes.map((item) => item.key)
                                                        ]);
                                                    }}
                                                />
                                            ) : (
                                                <NoContent
                                                    text="暂无集成回归用例"
                                                    className={styles.noContent}
                                                />
                                            )}
                                        </div>
                                    </Spin>
                                </div>
                            </Form.Item>
                        </div>
                        {/* Add the rest of the task info form items here */}
                    </Form>
                </div>
            )
        },
        {
            title: '设备配置',
            content: (
                <div>
                    {/* Android Configuration */}
                    {/* <CardTitle text="自动化小助手配置" /> */}
                    <div className={styles.cardLayout}>
                        <Tabs
                            type="card"
                            activeKey={activeExecuteConfigKey}
                            onChange={(key) => {
                                setActiveExecuteConfigKey(key);
                            }}
                            items={items}
                        />
                        <Form
                            key="android"
                            style={{
                                display: activeExecuteConfigKey !== 'android' ? 'none' : 'block'
                            }}
                            form={androidConfigForm}
                            layout="vertical"
                            requiredMark={false}
                            colon={false}
                        >
                            {/* 是否使用设备选择 */}
                            <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                            <DeviceType
                                cloudDeviceList={cloudDeviceList?.android ?? []}
                                form={androidConfigForm}
                                osType={1}
                                // planType={planType}
                            />
                            <Form.Item
                                noStyle
                                shouldUpdate={(prevValues, currentValues) =>
                                    prevValues.deviceType !== currentValues.deviceType
                                }
                            >
                                {({ getFieldValue }) => {
                                    return (
                                        <>
                                            {getFieldValue('deviceType') !== 2 && (
                                                <>
                                                    {/* 具体设备选择 */}
                                                    <CloudDevice
                                                        form={androidConfigForm}
                                                        cloudDeviceList={
                                                            cloudDeviceList?.android ?? []
                                                        }
                                                        osType={1}
                                                    />

                                                    {/* 执行重试次数 */}
                                                    <RetryTimes form={androidConfigForm} />
                                                    <EnvParams
                                                        form={androidConfigForm}
                                                        osType={1}
                                                        envList={envList?.android ?? []}
                                                        createPlanOption={createPlanOption}
                                                        setCreatePlanOption={(res) => {
                                                            setCreatePlanOption(res);
                                                        }}
                                                    />
                                                    {/* 是否安装包 */}
                                                    <InstallParams form={androidConfigForm} />

                                                    <Form.Item
                                                        label="其他配置"
                                                        name="otherConfig"
                                                        initialValue={0}
                                                        layout="horizontal"
                                                    >
                                                        <a
                                                            style={{
                                                                textDecoration: 'underline'
                                                            }}
                                                            onClick={() => {
                                                                androidConfigForm.setFieldValue(
                                                                    'otherConfig',
                                                                    !showOtherConfig
                                                                );
                                                                setShowOtherConfig(
                                                                    !showOtherConfig
                                                                );
                                                            }}
                                                        >
                                                            {showOtherConfig ? '收起' : '展开'}
                                                        </a>
                                                    </Form.Item>
                                                    <div
                                                        style={{
                                                            display: showOtherConfig
                                                                ? 'block'
                                                                : 'none'
                                                        }}
                                                    >
                                                        {/* 是否使用系统弹窗 */}
                                                        <SystemPop form={androidConfigForm} />
                                                        {/* 是否打点上报 */}
                                                        {getFieldValue('deviceType') === 3 && (
                                                            <LogCheck />
                                                        )}
                                                        {/* 是否日志收集 */}
                                                        <LogCat />
                                                        {/* 日志转发配置 */}
                                                        <IpRedirect
                                                            createPlanOption={createPlanOption}
                                                            setCreatePlanOption={
                                                                setCreatePlanOption
                                                            }
                                                            isExcecuteConfig={true}
                                                            osType={1}
                                                        />
                                                    </div>
                                                    {/* <CardTitle
                                                                text="提醒配置"
                                                                style={{ marginTop: 0 }}
                                                            /> */}
                                                </>
                                            )}
                                        </>
                                    );
                                }}
                            </Form.Item>
                        </Form>
                        <Form
                            key="ios"
                            style={{
                                display: activeExecuteConfigKey !== 'ios' ? 'none' : 'block'
                            }}
                            form={iosConfigForm}
                            layout="vertical"
                            requiredMark={false}
                            colon={false}
                        >
                            <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                            {/* 是否使用设备选择 */}
                            <DeviceType
                                cloudDeviceList={cloudDeviceList?.ios ?? []}
                                form={iosConfigForm}
                                osType={2}
                                // planType={planType}
                            />
                            <Form.Item
                                noStyle
                                shouldUpdate={(prevValues, currentValues) =>
                                    prevValues.deviceType !== currentValues.deviceType
                                }
                            >
                                {({ getFieldValue }) => {
                                    return (
                                        <>
                                            {getFieldValue('deviceType') !== 2 && (
                                                <>
                                                    {/* 具体设备选择 */}
                                                    {getFieldValue('deviceType') === 1 ? (
                                                        <CloudDevice
                                                            form={iosConfigForm}
                                                            cloudDeviceList={
                                                                cloudDeviceList?.ios ?? []
                                                            }
                                                            osType={2}
                                                        />
                                                    ) : (
                                                        <LocalDevice
                                                            form={iosConfigForm}
                                                            osType={2}
                                                        />
                                                    )}
                                                    {/* 执行重试次数 */}
                                                    <RetryTimes form={iosConfigForm} />
                                                    <EnvParams
                                                        form={iosConfigForm}
                                                        osType={2}
                                                        envList={envList?.ios ?? []}
                                                        createPlanOption={createPlanOption}
                                                        setCreatePlanOption={(res) => {
                                                            setCreatePlanOption(res);
                                                        }}
                                                    />
                                                    {/* 是否安装包 */}
                                                    {getFieldValue('deviceType') === 1 && (
                                                        <InstallParams form={iosConfigForm} />
                                                    )}

                                                    <Form.Item
                                                        label="其他配置"
                                                        name="otherConfig"
                                                        initialValue={0}
                                                        layout="horizontal"
                                                    >
                                                        <a
                                                            style={{
                                                                textDecoration: 'underline'
                                                            }}
                                                            onClick={() => {
                                                                iosConfigForm.setFieldValue(
                                                                    'otherConfig',
                                                                    !showOtherConfig
                                                                );
                                                                setShowOtherConfig(
                                                                    !showOtherConfig
                                                                );
                                                            }}
                                                        >
                                                            {showOtherConfig ? '收起' : '展开'}
                                                        </a>
                                                    </Form.Item>
                                                    <div
                                                        style={{
                                                            display: showOtherConfig
                                                                ? 'block'
                                                                : 'none'
                                                        }}
                                                    >
                                                        {/* 是否使用系统弹窗 */}
                                                        <SystemPop form={iosConfigForm} />
                                                        {/* 是否打点上报 */}
                                                        {getFieldValue('deviceType') === 3 && (
                                                            <LogCheck />
                                                        )}
                                                        {/* 是否日志收集 */}
                                                        <LogCat />
                                                        {/* 日志转发配置 */}
                                                        <IpRedirect
                                                            createPlanOption={createPlanOption}
                                                            setCreatePlanOption={
                                                                setCreatePlanOption
                                                            }
                                                            isExcecuteConfig={true}
                                                            osType={2}
                                                        />
                                                    </div>
                                                </>
                                            )}
                                        </>
                                    );
                                }}
                            </Form.Item>
                        </Form>
                        {+query.planType === 5 && activeExecuteConfigKey === 'server' ? (
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description="精准录制模式下暂不支持该配置"
                                style={{ marginBottom: '50px' }}
                            />
                        ) : (
                            <Form
                                style={{
                                    display: activeExecuteConfigKey === 'server' ? 'block' : 'none'
                                }}
                                form={serverConfigForm}
                                layout="vertical"
                                requiredMark={false}
                                colon={false}
                                initialValues={{
                                    modeType: 0
                                }}
                            >
                                <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                                {/* 是否使用设备选择 */}
                                <DeviceType
                                    cloudDeviceList={poolList ?? []}
                                    form={serverConfigForm}
                                    osType={4}
                                    // planType={planType}
                                />
                                <Form.Item
                                    noStyle
                                    shouldUpdate={(prevValues, currentValues) =>
                                        prevValues.modeType !== currentValues.modeType ||
                                        prevValues.deviceType !== currentValues.deviceType
                                    }
                                >
                                    {({ getFieldValue }) => {
                                        const modeType = getFieldValue('modeType');
                                        return (
                                            <>
                                                {getFieldValue('deviceType') !== 2 && (
                                                    <>
                                                        <Form.Item
                                                            label="执行器集群"
                                                            name="poolId"
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择执行器集群'
                                                                }
                                                            ]}
                                                        >
                                                            <Select
                                                                placeholder="请选择执行器集群"
                                                                options={poolList.map((item) => ({
                                                                    value: item.poolId,
                                                                    label: item.poolName // 显示服务器名称和地址
                                                                }))}
                                                                allowClear
                                                            />
                                                        </Form.Item>
                                                        <CardTitle
                                                            text="执行模式"
                                                            style={{ marginTop: 0 }}
                                                        />

                                                        <Form.Item
                                                            // label="执行模式"
                                                            name="modeType"
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择执行模式'
                                                                }
                                                            ]}
                                                            initialValue={0}
                                                        >
                                                            <Radio.Group>
                                                                <Radio value={0}>普通模式</Radio>
                                                                <Radio value={1}>Diff 模式</Radio>
                                                            </Radio.Group>
                                                        </Form.Item>
                                                        {modeType === 0 ? (
                                                            <EnvParams
                                                                form={serverConfigForm}
                                                                osType={4}
                                                                envList={envList?.server ?? []}
                                                                createPlanOption={createPlanOption}
                                                                setCreatePlanOption={(res) => {
                                                                    setCreatePlanOption(res);
                                                                }}
                                                            />
                                                        ) : null}
                                                        {modeType === 1 ? (
                                                            <>
                                                                <CardTitle text="Server 配置" />
                                                                <Form.Item
                                                                    label="稳定版环境"
                                                                    name="stableEnvironmentId"
                                                                >
                                                                    <Select
                                                                        placeholder="请选择稳定版环境"
                                                                        options={
                                                                            envList?.server?.map(
                                                                                (item) => {
                                                                                    return {
                                                                                        label: item.envName,
                                                                                        value: item.envId
                                                                                    };
                                                                                }
                                                                            ) ?? []
                                                                        }
                                                                        allowClear
                                                                    />
                                                                </Form.Item>
                                                                <Form.Item
                                                                    label="待测版环境"
                                                                    name="testEnvironmentId"
                                                                >
                                                                    <Select
                                                                        placeholder="请选择待测版环境"
                                                                        options={
                                                                            envList?.server?.map(
                                                                                (item) => {
                                                                                    return {
                                                                                        label: item.envName,
                                                                                        value: item.envId
                                                                                    };
                                                                                }
                                                                            ) ?? []
                                                                        }
                                                                        allowClear
                                                                    />
                                                                </Form.Item>
                                                                <CardTitle text="断言配置" />
                                                                <Form.Item
                                                                    label="JSON Schema 校验"
                                                                    tooltip="开启后，将使用开启的 JSON Schema 断言对各服务的返回结果做校验"
                                                                    name="jsonSchemaCheck"
                                                                >
                                                                    <Switch />
                                                                </Form.Item>
                                                                <Form.Item
                                                                    label="智能去噪"
                                                                    name="intelligentNoiseReduce"
                                                                    tooltip="开启后，在 Diff 忽略 Key 断言失败时，会智能计算噪声 Key 并回写到断言，再次执行 Diff 忽略 Key 断言校验"
                                                                >
                                                                    <Switch />
                                                                </Form.Item>
                                                            </>
                                                        ) : null}
                                                    </>
                                                )}
                                            </>
                                        );
                                    }}
                                </Form.Item>
                            </Form>
                        )}
                    </div>
                </div>
            )
        },
        {
            title: '报警策略',
            content: (
                <>
                    <Tabs
                        type="card"
                        items={items}
                        activeKey={activeAlarmConfigKey}
                        onChange={setActiveAlarmConfigKey}
                    />
                    <AlarmConfigForm activeKey="android" key="android" form={androidAlarmForm} />
                    <AlarmConfigForm activeKey="ios" key={'ios'} form={iosAlarmForm} />
                    <AlarmConfigForm activeKey="server" key={'server'} form={serverAlarmForm} />
                </>
            )
        }
    ];
    console.log('treeData', treeData);

    return (
        <Spin spinning={createLoading}>
            <CardContent>
                {contextHolder}
                <Descriptions column={1} size="small" />
                <div className={styles.header}>
                    <CardHeader
                        text={query?.planId ? '更新配置' : '新建计划'}
                        extra={
                            <>
                                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                    {currentStep > 0 && (
                                        <Button
                                            style={{ margin: '0 8px' }}
                                            onClick={() => setCurrentStep(currentStep - 1)}
                                        >
                                            上一步
                                        </Button>
                                    )}
                                    {currentStep < steps.length - 1 && (
                                        <Button
                                            type="primary"
                                            onClick={async () => {
                                                try {
                                                    let newItems;
                                                    if (currentStep === 0) {
                                                        await commonConfigForm.validateFields();
                                                        console.log(
                                                            commonConfigForm.getFieldsValue(),
                                                            123
                                                        );
                                                        const commonConfigFormValues =
                                                            await commonConfigForm.getFieldsValue();
                                                        setFirstStepData(commonConfigFormValues);
                                                        const numbers = new Set(
                                                            commonConfigFormValues?.nodeTree
                                                                .filter(
                                                                    (item) =>
                                                                        typeof item === 'string'
                                                                )
                                                                .map((item) => item.split('-')[1])
                                                        );
                                                        newItems = executeConfigItems.filter(
                                                            (item) => numbers.has(item.value)
                                                        );
                                                        setItems(newItems);
                                                        if (
                                                            !newItems.find(
                                                                (item) =>
                                                                    item.key ===
                                                                    activeExecuteConfigKey
                                                            )
                                                        ) {
                                                            setActiveExecuteConfigKey(
                                                                newItems[0]?.key
                                                            );
                                                        }
                                                    }
                                                    if (currentStep === 1) {
                                                        const osTypeList = items.map(
                                                            (item) => item.key
                                                        );
                                                        // 动态校验表单
                                                        if (osTypeList.includes('android')) {
                                                            await androidConfigForm?.validateFields();
                                                        }
                                                        if (osTypeList.includes('ios')) {
                                                            await iosConfigForm?.validateFields();
                                                        }
                                                        if (osTypeList.includes('server')) {
                                                            await serverConfigForm?.validateFields();
                                                        }
                                                        // 设置下一步的数据
                                                        setSecondsStepData({
                                                            android: osTypeList.includes('android')
                                                                ? androidConfigForm?.getFieldsValue()
                                                                : null,
                                                            ios: osTypeList.includes('ios')
                                                                ? iosConfigForm?.getFieldsValue()
                                                                : null,
                                                            server: osTypeList.includes('server')
                                                                ? serverConfigForm?.getFieldsValue()
                                                                : null
                                                        });
                                                        // 修复：确保activeAlarmConfigKey为用户实际选择的终端类型的第一个
                                                        if (osTypeList.length > 0) {
                                                            setActiveAlarmConfigKey(osTypeList[0]);
                                                        }
                                                    }
                                                    setCurrentStep(currentStep + 1);
                                                } catch (error) {
                                                    console.log(error, 'error');
                                                    messageApi.error('请检查表单填写是否正确');
                                                }
                                            }}
                                        >
                                            下一步
                                        </Button>
                                    )}
                                    {currentStep === steps.length - 1 && (
                                        <div>
                                            {query?.planId ? (
                                                <Popconfirm
                                                    placement="right"
                                                    title="您确定要更新测试计划的配置？"
                                                    description="特别注意: 若取消勾选的用例将清除所有执行数据"
                                                    onConfirm={onClick}
                                                    cancelText="取消"
                                                    okText="确认"
                                                >
                                                    <Button type="primary">更新</Button>
                                                </Popconfirm>
                                            ) : (
                                                <Button type="primary" onClick={onClick}>
                                                    创建
                                                </Button>
                                            )}
                                        </div>
                                    )}
                                    <Button
                                        onClick={() => {
                                            navigate(-1);
                                        }}
                                    >
                                        取消
                                    </Button>
                                </div>
                            </>
                        }
                    />
                </div>
                {/* <div className={styles.container}>
                    <Form
                        form={commonConfigForm}
                        layout="vertical"
                        requiredMark={false}
                        colon={false}
                    ></Form>
                </div> */}
                <CaseNodeTree
                    ref={caseNodeTreeModalRef}
                    caseNodeList={caseNodeList}
                    modalType={query?.planId ? 'edit' : 'create'}
                    setCaseNodeList={(e) => {
                        setCaseNodeList(e);
                    }}
                    filterType={[2]}
                />
                <Steps current={currentStep} style={{ marginTop: 50, marginBottom: 20 }}>
                    {steps?.map((item) => (
                        <Steps.Step key={item.title} title={item.title} />
                    ))}
                </Steps>
                <div className={styles.stepsContent}>{steps[currentStep].content}</div>
            </CardContent>
            <CreatTemplateModal ref={creatTemplateModalRef} />
            <SettingModal ref={templateManagerRef} />
            {/* <TemplateManager ref={templateManagerRef} /> */}
            {/* <SetCenter runCaseSettingRef={runCaseSettingRef} settingModalRef={settingModalRef} />
            <SettingModal ref={settingModalRef} /> */}
        </Spin>
    );
}

export default connectModel([baseModel, planModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule,
    planList: state.common.plan.planList,
    currentPlanGroup: state.common.plan.currentPlanGroup,
    serverList: state.common.case.serverList,
    currentPlan: state.common.plan.currentPlan
}))(CreateGroupPage);
