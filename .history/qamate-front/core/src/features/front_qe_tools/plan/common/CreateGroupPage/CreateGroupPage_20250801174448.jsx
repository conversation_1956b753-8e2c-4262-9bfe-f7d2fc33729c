import { useEffect, useState, useMemo, useRef } from 'react';
import { useNavigate, useLocation } from 'umi';
import { stringifyUrl } from 'query-string';
import {
    Button,
    Descriptions,
    Form,
    Input,
    message,
    Radio,
    Popconfirm,
    Tabs,
    Tag,
    Tree,
    Spin,
    Tooltip,
    Badge,
    Select,
    Checkbox,
    Empty,
    Switch
} from 'antd';
import {
    AndroidOutlined,
    AppleOutlined,
    DownOutlined,
    FilterOutlined,
    FunnelPlotOutlined,
    CloudServerOutlined
} from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { deepcopy } from 'COMMON/components/TreeComponents/Step/utils';
import { connectModel } from 'COMMON/middleware';
import { post } from 'COMMON/utils/requestUtils';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import commonModel from 'COMMON/models/commonModel';
import { CardContent } from 'COMMON/components/common/Card';
import { <PERSON>Header } from 'COMMON/components/common/Card';
import { CardTitle } from 'COMMON/components/common/Card';
import NoContent from 'COMMON/components/common/NoContent';
import NodeIcon from 'COMMON/components/NewAddDropdown/components/NodeIcon';
import { planTypeOptions } from 'COMMON/utils/planUtils.js';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import Filter from 'COMMON/components/Filter';
import { convertTagColorTypeToHexadecimal } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { FILTER_INFO } from 'COMMON/components/Filter/const';
import RenderTitle from 'FEATURES/front_qe_tools/plan/components/RenderTitle';
import {
    getNewIpRedirect,
    getNewIpRedirectReverse
} from 'COMMON/components/TreeComponents/Step/utils';
import {
    createPlan,
    updatePlan,
    getPlanDetail,
    getPlanList,
    getDevicePoolList,
    getPlanTemplateDetail,
    getPlanTemplateList
} from 'COMMON/api/front_qe_tools/plan/plan';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getTreeNodeList } from 'COMMON/api/front_qe_tools/tree';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import { getGroupList } from 'COMMON/api/front_qe_tools/group';
import { FILTER_DATA, filterObject } from 'COMMON/utils/commonUtils';
import CloudDevice from 'COMMON/components/execution/CloudDevice';
import InstallParams from 'COMMON/components/execution/InstallParams';
import IpRedirect from 'FEATURES/front_qe_tools/plan/components/Setting/IpRedirect';
import RetryTimes from 'FEATURES/front_qe_tools/plan/components/Setting/RetryTimes';
import LocalDevice from 'FEATURES/front_qe_tools/plan/components/Setting/LocalDevice';
import DeviceType from 'FEATURES/front_qe_tools/plan/components/Setting/DeviceType';
import SystemPop from 'FEATURES/front_qe_tools/plan/components/Setting/SystemPop';
import TaskTimeout from 'FEATURES/front_qe_tools/plan/components/Setting/TaskTimeout';
import LogCheck from 'FEATURES/front_qe_tools/plan/components/Setting/LogCheck';
import LogCat from 'FEATURES/front_qe_tools/plan/components/Setting/LogCat';
import EnvParams from 'FEATURES/front_qe_tools/plan/components/Setting/EnvParams';

import CaseNodeTree from 'FEATURES/front_qe_tools/plan/components/Setting/CaseNodeTree';
import CreatTemplateModal from 'FEATURES/front_qe_tools/plan/components/CreatTemplateModal';
import NotificationConfig from 'FEATURES/front_qe_tools/plan/components/Setting/NotificationConfig';
import styles from './CreateGroupPage.module.less';

const { Search } = Input;

// 扁平树为数组
function flattenTree(node, parentId = null, flattenedTree = []) {
    const nodeCopy = deepcopy(node);
    nodeCopy.parentId = parentId;
    flattenedTree.push(nodeCopy);

    const children = node.children || [];
    for (const child of children) {
        flattenTree(child, node.nodeId, flattenedTree);
    }

    return flattenedTree;
}

// 在树结构中查找指定节点
function findNodeInTree(tree, nodeId) {
    for (const node of tree) {
        if (node.nodeId === nodeId) {
            return node;
        }
        if (node.children && node.children.length > 0) {
            const found = findNodeInTree(node.children, nodeId);
            if (found) {
                return found;
            }
        }
    }
    return null;
}

const parseFilterData = (filterData = {}) => {
    // 字段映射关系及类型转换规则
    const fieldMapping = {
        isAccess: 'access',
        priority: 'priority',
        tagInfo: 'tag',
        executionType: 'executeType',
        record: 'record'
    };

    const originalData = {};
    Object.entries(filterData)?.forEach(([key, filterItem]) => {
        const field = fieldMapping[key];
        if (!field) {
            return;
        }
        // 类型转换逻辑
        originalData[field] = {
            data: filterItem.values.map((v) => {
                return +v;
            }),
            activeKey: 'belong'
        };
    });

    return originalData;
};

// 从树结构中提取选中的节点，并生成包含任务的节点ID列表
function getSelectedNodeKeys(
    tree,
    treeNodeList,
    nodeList = [],
    filterDataMap = {},
    isNodeChange = {},
    nodeCaseKesMap = {}
) {
    tree?.forEach((item) => {
        if (item.nodeType !== 1) {
            let node = treeNodeList?.find((v) => v.treeNodeId === item.nodeId);
            if (node) {
                node?.taskList?.forEach((task) => {
                    nodeList.push(item.nodeId + '-' + task.osType);
                });
            }
            // 父级节点筛选条件
            let task = item.filterList?.find((v) => v);
            filterDataMap[item.nodeId] = parseFilterData(task);
            [1, 2, 3, 4, 5].forEach((os) => {
                const filter = item.filterList?.find((v) => v.osType === os);
                const id = `${item.nodeId}-${os}`;
                filterDataMap[id] = filter ? parseFilterData(filter) : {};
                if (filter && !isEmpty(filter)) {
                    // 默认都是改过
                    isNodeChange[id] = false;
                    nodeCaseKesMap[id] = [
                        ...(item?.caseNodeList?.find((v) => v.osType === os)?.caseNodeIdList || [])
                    ];
                } else {
                    isNodeChange[id] = true;
                    // 存储之前的caseNodeList
                }
            });
        } else {
            getSelectedNodeKeys(
                item.children,
                treeNodeList,
                nodeList,
                filterDataMap,
                isNodeChange,
                nodeCaseKesMap
            );
        }
    });
    return nodeList;
}

const getLinkDescList = (tree, linkDescList = []) => {
    tree.forEach((element) => {
        if (!isEmpty(element?.extra?.linkList)) {
            for (let link of element?.extra?.linkList) {
                if (!linkDescList?.includes(link?.name)) {
                    linkDescList.push(link?.name);
                }
            }
        }
        getLinkDescList(element?.children ?? [], linkDescList);
    });
    return linkDescList;
};

function CreateGroupPage(props) {
    const {
        currentSpace,
        currentModule,
        serverList,
        currentPlanGroup,
        planList,
        setPlanList,
        currentPlan,
        setCurrentPlan,
        tagList,
        username
    } = props;
    const query = getQueryParams();
    const location = useLocation();
    const [currentTemplateId, setCurrentTemplateId] = useState(null);
    const [loading, setLoading] = useState(true);
    const [groupId, setGroupId] = useState(null);
    const [serverShowMore, setServerShowMore] = useState(false);
    const [iosShowMore, setIosShowMore] = useState(false);
    const [androidShowMore, setAndroidShowMore] = useState(false);
    const [createLoading, setCreateLoading] = useState(false);
    const [selectedNode, setSelectedNode] = useState([]);
    const [caseNodeList, setCaseNodeList] = useState([]);
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const [directoryTreeData, setDirectoryTreeData] = useState([]);
    const [nodeTreeMapList, setNodeTreeMapList] = useState([]);
    const [planDetail, setPlanDetail] = useState({});
    const [groupList, setGroupList] = useState([]);
    const [filteredList, setFilteredList] = useState([]);
    const [cloudDeviceList, setCloudDeviceList] = useState({}); // 双端云设备列表
    const [envList, setEnvList] = useState([]); // 环境列表
    const [showOtherConfig, setShowOtherConfig] = useState(false); // 是否显示其他配置
    const [activeExecuteConfigKey, setActiveExecuteConfigKey] = useState('android');
    const caseNodeTreeModalRef = useRef();
    const creatTemplateModalRef = useRef();
    const templateManagerRef = useRef();
    const [filterDataMap, setFilterDataMap] = useState({});
    const [treeNodaPathMap, setTreeNodaPathMap] = useState({});
    const [nodeCaseListMap, setNodeCaseListMap] = useState({});
    const [nodeCaseKesMap, setNodeCaseKesMap] = useState({});
    const [nodeCaseIsChangeMap, setNodeCaseIsChangeMap] = useState({});
    const [filterDataIsChangeMap, setFilterDataIsChangeMap] = useState({});
    const [selectNodeOrhalfCheckedKeys, setSelectNodeOrhalfCheckedKeys] = useState([]);
    const [createPlanOption, setCreatePlanOption] = useState({
        planName: null,
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                envParams: {}
            }
        }
    });
    const [commonConfigForm] = Form.useForm();
    const [androidConfigForm] = Form.useForm();
    const [serverConfigForm] = Form.useForm();
    const [iosConfigForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    // const [creationMethod, setCreationMethod] = useState('custom');
    const [creationMethod, setCreationMethod] = useState('custom');
    const [poolList, setPoolList] = useState([]); // 设备池列表
    const [templateList, setTemplateList] = useState([]); // 模板列表
    const navigate = useNavigate();
    // 标签选项（动态变更）
    const getTagOptions = (isLabelName) => {
        tagList.sort((a, b) => (a.username === username ? -1 : b.username === username ? 1 : 0));
        return tagList.map((item, index) => {
            const color = convertTagColorTypeToHexadecimal(item.color);
            return {
                key: 'tag' + index,
                value: item.id,
                name: item?.tagName,
                username: item.username,
                label: (
                    <>
                        {isLabelName ? (
                            <>
                                {item.tagName}({item?.username})
                            </>
                        ) : (
                            <>
                                <div
                                    className={styles.tagIcon}
                                    style={{
                                        backgroundColor: color,
                                        border: '#d9d9d9 solid 1px'
                                    }}
                                />
                                <div className={styles.tagName}>
                                    {item.tagName}
                                    <span className={styles.tagCreator}>({item?.username})</span>
                                </div>
                            </>
                        )}
                    </>
                )
            };
        });
    };

    const getOptions = (type, isLabelName = false) => {
        if (type === 'tag') {
            return getTagOptions(isLabelName);
        }
        if (type === 'link') {
            return [];
        }
        return FILTER_INFO[type]?.option;
    };

    const getNodeList = (nodeTree, nodeList = []) => {
        nodeTree.forEach((item) => {
            if (!item?.children || item.children.length === 0) {
                nodeList.push(item.nodeId);
            }
            if (item?.children && item.children.length > 0) {
                getNodeList(item.children, nodeList);
            }
        });
        return nodeList;
    };
    const getFilterData = (nodeId, newFilterDataMap, isLabel = false) => {
        const filterTagList = [];
        const parentIdPath = [...(treeNodaPathMap[nodeId]?.parentPath || [])]; // 获取当前节点的父节点路径
        parentIdPath.push(nodeId);
        let filterData = filterObject(FILTER_DATA, [
            'stampResult',
            'autoStatus',
            'relation',
            'disabled',
            'creator'
        ]);
        let isEnd = false;
        parentIdPath?.reverse().forEach((itemNodeId) => {
            // 忽略包含 '-' 的节点和自身节点
            if (itemNodeId !== 'root' && !isEnd && !`${itemNodeId}`.includes('-')) {
                // 合并选中结果
                const currentFilterData = (newFilterDataMap || filterDataMap)[itemNodeId];
                if (currentFilterData) {
                    Object.keys(filterData).forEach((type) => {
                        filterData[type] = {
                            ...filterData[type],
                            data: [
                                ...new Set([
                                    ...(filterData[type]?.data || []),
                                    ...(currentFilterData[type]?.data || []) // 添加默认空数组
                                ])
                            ]
                        };
                    });
                }
            }
            if (filterDataIsChangeMap[itemNodeId] === true && !`${itemNodeId}`.includes('-')) {
                isEnd = true;
            }
        });
        // 获取数组最后一项

        // const firstParentId = [...parentIdPath].pop();
        // if(parentIdPath.length >= 1){
        //     const firstParentId = parentIdPath[parentIdPath.length - 1]
        //     filterData = (newFilterDataMap || filterDataMap)[firstParentId.nodeId]
        // }
        if (isLabel) {
            Object.keys(filterData).forEach((type) => {
                filterTagList.push(
                    ...filterData[type].data.map((v) => {
                        return getOptions(type, true).find((op) => op.value === v).label;
                    })
                );
            });
        }

        return {
            filterTagList,
            filterData
        };
    };
    // const linkDescOptions = useMemo(() => {
    //     let options = getLinkDescList(Object.values(nodeCaseListMap || {}));
    //     return options.map((item, index) => ({
    //         key: 'link_desc' + index,
    //         value: item,
    //         label: item,
    //     }));
    // }, [nodeCaseListMap]);
    const getLeafNodeIds = (tree, keys = []) => {
        tree.map((ele) => {
            if (!ele.children || ele.children.length === 0) {
                keys.push(ele.nodeId);
            } else {
                getLeafNodeIds(ele.children, keys);
            }
        });
        return keys;
    };
    const selectGroupType = useMemo(() => {
        if (filteredList?.length > 0 && groupId) {
            return filteredList.find((item) => item.groupId === groupId)?.groupType;
        }
    }, [groupId, filteredList]);
    const getNewTree = (tree) => {
        return tree.map((item) => {
            if (item.nodeType === 2) {
                let _osTypeList;
                if (item.signType === 1 && item.osTypeList[0] === 3) {
                    _osTypeList = [3];
                } else if (item.signType === 1 && item.osTypeList.length > 1) {
                    _osTypeList = [item.osTypeList[0]];
                } else if (item.signType === 0 && item.osTypeList[0] === 3) {
                    _osTypeList = [1, 2];
                } else if (item.signType === 0 && item.osTypeList.length > 1) {
                    _osTypeList = item.osTypeList;
                } else {
                    _osTypeList = item.osTypeList;
                }
                item.children = _osTypeList.map((os) => ({
                    nodeId: item.nodeId + '-' + os,
                    os: os,
                    caseRootId: item.caseRootId,
                    caseNodeList: [],
                    signType: item.signType
                }));
                return item;
            } else {
                return { ...item, children: getNewTree(item.children) };
            }
        });
    };
    // 获取模板列表的函数
    const fetchTemplates = async () => {
        const { templateList } = await getPlanTemplateList({
            moduleId: currentSpace?.id,
            templateType: currentPlanGroup?.planType
        });
        setTemplateList(templateList ?? []);
    };

    // useEffect(() => {
    //     if (creationMethod === 'template') {
    //         fetchTemplates();
    //     }
    // }, [creationMethod]);
    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            // 获取目录树
            let { groupList } = await getGroupList({
                moduleId: currentSpace?.id,
                isArchive: 0
            });
            if (isEmpty(groupList)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setGroupList(groupList);
            let filter = [];
            if (+query?.planType === 1) {
                // 集成回归测试情况下 展示集成回归用例组和本地执行用例组
                filter = groupList.filter(
                    (group) => +group.groupType === 2 || +group.groupType === 4
                );
            } else if (location.pathname.includes('daily')) {
                // 定时巡检测试情况下 展示集成回归用例组
                filter = groupList.filter((group) => +group.groupType === 2);
            } else {
                // 通用测试情况下 展示所有用例组
                filter = groupList;
            }
            setFilteredList(filter);
            setGroupId(filter[0]?.groupId);
            getTreeNodeListWithPlanType(filter[0]?.groupId, groupList);
            // console.log(filteredList, 'filteredList[0]?.groupId');
            // getTreeNodeListWithPlanType(filteredList[0]?.groupId, groupList);
            // 获取设备池列表
            const { poolList } = await getDevicePoolList({
                moduleId: currentSpace?.id,
                poolType: 3 // 设备池类型 1-Android 2-iOS 3-Server
            });
            setPoolList(poolList);
        }
        func();
    }, [currentSpace?.id, query?.planType]);

    //   创建节点树关系
    function buildPathTree(node, parentPath, pathTree) {
        const currentNodeId = node.nodeId;
        const children = node.children || [];

        // 先处理子节点，后序遍历确保子节点信息已生成
        for (const child of children) {
            const childParentPath = [...parentPath, currentNodeId];
            buildPathTree(child, childParentPath, pathTree);
        }

        // 收集所有子节点ID（包括后代）
        let childrenNodeIdTree = [];
        for (const child of children) {
            childrenNodeIdTree.push(child.nodeId);
            childrenNodeIdTree.push(...(pathTree[child.nodeId]?.childrenNodeIdTree || []));
        }

        // 记录当前节点的路径信息
        pathTree[currentNodeId] = {
            parentPath: [...parentPath],
            childrenNodeIdTree: [...new Set(childrenNodeIdTree)] // 去重（根据需求可选）
        };
    }

    // 生成组建路径映射表
    const createTreePath = (treeData) => {
        const pathTree = {};
        // 遍历所有根节点
        treeData.forEach((rootNode) => {
            buildPathTree(rootNode, [], pathTree);
        });
        setTreeNodaPathMap(pathTree);
    };
    const getTreeNodeListWithPlanType = async (groupId, groupList) => {
        try {
            setLoading(true);
            let group;
            //  集成回归用例组
            // if (planType === 1) {
            //     group = groupList.find(item => item?.groupType === 2);
            // }
            // //  本地执行用例组
            // if (planType === 2) {
            //     group = groupList.find(item => item?.groupType === 4);
            // }
            // group = groupList.find(item => item?.groupType === planType);
            if (!groupId) {
                return;
            }
            let { tree } = await getTreeNodeList({ groupId: groupId });
            setDirectoryTreeData(getNewTree(tree));
            setSearchValue('');
            let nodeKeys = tree.map((item) => flattenTree(item)).flat() ?? [];
            setNodeTreeMapList(nodeKeys);
            // 转化成map
            const newNodeCaseListMap = {};
            nodeKeys.forEach((v) => {
                newNodeCaseListMap[v.nodeId] = v;
            });
            setNodeCaseListMap(newNodeCaseListMap);
            createTreePath(getNewTree(tree));

            if (!query?.planId) {
                const initFilterDataMap = {};
                nodeKeys.forEach((item) => {
                    if (!selectedNode.includes(item.nodeId)) {
                        initFilterDataMap[item.nodeId] = filterObject(FILTER_DATA, [
                            'stampResult',
                            'autoStatus'
                        ]);
                    }
                });
                setFilterDataMap(initFilterDataMap);
            }
            // 获取 创建第一次进入默认全部key
            setExpandedKeys(nodeKeys.map((item) => item.nodeId));
            setLoading(false);
        } catch (err) {
            console.log(err);
        }
    };

    useEffect(() => {
        if (
            !selectNodeOrhalfCheckedKeys.length &&
            selectedNode.length > 0 &&
            !isEmpty(treeNodaPathMap)
        ) {
            const newSelectNodeOrhalfCheckedKeys = [];
            // 获取所有父级节点
            selectedNode.forEach((item) => {
                const parentPath = treeNodaPathMap[item]?.parentPath;
                if (parentPath) {
                    newSelectNodeOrhalfCheckedKeys.push(item, ...parentPath);
                }
            });

            setSelectNodeOrhalfCheckedKeys([...new Set(newSelectNodeOrhalfCheckedKeys)]);
        }
    }, [selectedNode, selectNodeOrhalfCheckedKeys, treeNodaPathMap]);
    // useEffect(() => {
    //     // console.log(groupList, groupId, 111111);
    //     if (groupList && groupId) {
    //         getTreeNodeListWithPlanType(groupId, groupList);
    //     }
    // }, [groupId, filteredList]);
    // useEffect(() => {
    //     setGroupId(filteredList[0]?.groupId);
    // }, [filteredList]);
    // 获取已创建用例的对应内容
    const getExecutePlanCaseNodeList = (tree, nodeList = []) => {
        tree?.map((item) => {
            // 用例
            if (item?.nodeType === 2) {
                for (let _item of item?.caseNodeList ?? []) {
                    nodeList.push({
                        nodeId: item.nodeId,
                        caseRootId: item.caseRootId,
                        osType: _item.osType,
                        caseIdList: _item?.caseNodeIdList ?? [],
                        signType: item.signType
                    });
                }
            }
            if (!isEmpty(item.children) && item.nodeType === 1) {
                getExecutePlanCaseNodeList(item.children, nodeList);
            }
        });
        return nodeList;
    };

    useEffect(() => {
        if (query?.planId) {
            let currentPlanDetail = planList?.find((item) => item?.id === +query?.planId);
            if (!currentPlanDetail?.id) {
                return;
            }
            getPlanDetail({ planIdList: [+currentPlanDetail?.id] }).then((res) => {
                const planDetail = res.planList[0];
                const filterData = {};
                const nodeIsChangeMap = {};
                const nodeCaseKesMap = {};
                let nodeList = getSelectedNodeKeys(
                    planDetail?.nodeTree ?? [],
                    planDetail?.treeNodeList,
                    [],
                    filterData,
                    nodeIsChangeMap,
                    nodeCaseKesMap
                );
                // let seleckey = nodeList.map(item => Number(item?.split('-')[0]))
                // setSelectNodeOrhalfCheckedKeys(seleckey);
                // seleckey转成数字类型
                // console.log(nodeList, 'nodeListnodeList');
                setFilterDataMap(filterData);
                setFilterDataIsChangeMap(getNodeIsChangeMap(filterData));
                setNodeCaseIsChangeMap(nodeIsChangeMap);
                setNodeCaseKesMap(nodeCaseKesMap);
                setSelectedNode(nodeList);
                let caseList = getExecutePlanCaseNodeList(planDetail?.nodeTree ?? []);
                setCaseNodeList(caseList);
                setExpandedKeys([
                    ...(planDetail?.nodeTree
                        ? expandedKeys.filter((item) =>
                              nodeList.map((v) => +v.split('-')[0]).includes(item)
                          )
                        : expandedKeys)
                ]);
            });
        }
    }, [planList]);

    const getNodeIsChangeMap = (filterMap) => {
        const nodeIds = Object.keys(filterMap).filter((v) => `${v}`.includes('-'));
        const filterMapChange = {};
        nodeIds.forEach((v) => {
            filterMapChange[v] = true;
        });
        return filterMapChange;
    };

    useEffect(() => {
        if (commonConfigForm && query?.planId) {
            let currentPlanDetail = planList?.find((item) => item?.id === +query?.planId);
            if (!currentPlanDetail?.id) {
                return;
            }
            getPlanDetail({ planIdList: [+currentPlanDetail?.id] }).then((res) => {
                const planDetail = res.planList[0];
                const filterData = {};
                const nodeIsChangeMap = {};
                const nodeCaseKesMap = {};
                // console.log(cloneDeep(planDetail?.nodeTree), 'planDetail66666663');
                let nodeList = getSelectedNodeKeys(
                    planDetail?.nodeTree ?? [],
                    planDetail?.treeNodeList,
                    [],
                    filterData,
                    nodeIsChangeMap,
                    nodeCaseKesMap
                );
                setSelectedNode(nodeList);
                // 处理筛选条件
                setFilterDataMap(filterData);
                setFilterDataIsChangeMap(getNodeIsChangeMap(filterData));
                setNodeCaseIsChangeMap(nodeIsChangeMap);
                setNodeCaseKesMap(nodeCaseKesMap);
                // 去掉nodeList里面的- osType 部分，然后设置半选状态
                // let seleckey = nodeList.map(item => Number(item?.split('-')[0]))
                // setSelectNodeOrhalfCheckedKeys(seleckey);
                // console.log(seleckey, '半选');
                // 初始化的时候，把原有的数据填入表单
                commonConfigForm.setFieldValue('name', planDetail?.name ?? '');
                commonConfigForm.setFieldValue('nodeTree', nodeList ?? '');
                commonConfigForm.setFieldValue('cron', planDetail?.cron ?? null);
                const currentSelectedCaseNodeList = getSelectedCaseNodeList(planDetail);
                setCaseNodeList(currentSelectedCaseNodeList);
                setPlanDetail(planDetail);
                setExpandedKeys([
                    ...(planDetail?.nodeTree
                        ? expandedKeys.filter((item) =>
                              nodeList.map((v) => +v.split('-')[0]).includes(item)
                          )
                        : expandedKeys)
                ]);
            });
        }
    }, [commonConfigForm, planList]);

    useEffect(() => {
        if (androidConfigForm) {
            androidConfigForm.setFieldValue('deviceType', 2);
            androidConfigForm.setFieldValue('mockSwitch', true);
        }
    }, [androidConfigForm]);

    useEffect(() => {
        if (iosConfigForm) {
            iosConfigForm.setFieldValue('deviceType', 2);
            iosConfigForm.setFieldValue('mockSwitch', true);
        }
    }, [iosConfigForm]);
    useEffect(() => {
        obtainCloudDeviceList();
        obtaionEnvParamsList();
    }, []);

    const onClick = async () => {
        try {
            await commonConfigForm?.validateFields();
            await androidConfigForm?.validateFields();
            await iosConfigForm?.validateFields();
            await serverConfigForm?.validateFields();
        } catch (e) {
            messageApi.warning('请填写完整表单');
            return;
        }
        try {
            const commonConfigFormValues = commonConfigForm.getFieldsValue();
            let newNodeTree =
                commonConfigFormValues?.nodeTree?.filter((item) => ('' + item).includes('-')) || [];
            let treeNodeIdList = getTreeNodeIdList(
                directoryTreeData,
                [...new Set(newNodeTree?.map((item) => +(item + '').split('-')[0]))],
                newNodeTree
            );

            // 验证用户是否已经进行了筛选操作
            let hasUnfilteredNodes = false;
            let unfilteredNodeNames = [];

            for (let item of treeNodeIdList) {
                let caseDetail = caseNodeList?.find(
                    (it) => it?.nodeId === item?.treeNodeId && it.osType === item?.osType
                );
                if (!caseDetail || !caseDetail.caseIdList || caseDetail.caseIdList.length === 0) {
                    hasUnfilteredNodes = true;
                    // 获取节点名称用于提示
                    const nodeInfo = findNodeInTree(directoryTreeData, item?.treeNodeId);
                    if (nodeInfo) {
                        unfilteredNodeNames.push(nodeInfo.title);
                    }
                }
            }

            if (hasUnfilteredNodes) {
                messageApi.warning(
                    `请点击筛选按钮，为以下节点选择具体的测试用例：${unfilteredNodeNames.join('、')}`
                );
                return;
            }

            let planId;
            for (let item of treeNodeIdList) {
                let caseDetail = caseNodeList?.find(
                    (it) => it?.nodeId === item?.treeNodeId && it.osType === item?.osType
                );
                if (caseDetail) {
                    item.caseNodeList = caseDetail.caseIdList;
                } else {
                    item.caseNodeList = [];
                }
                // 没有改过
                if (!nodeCaseIsChangeMap[`${item.treeNodeId}-${item.osType}`]) {
                    if (caseDetail?.filter) {
                        item.filter = caseDetail.filter;
                    } else {
                        // console.log(item, '兜底逻辑', nodeCaseListMap[item.treeNodeId]);
                        // 兜底逻辑，如果没有改变过筛选条件，则沿用之前的筛选条件
                        const { filterData } = getFilterData(
                            `${item.treeNodeId}-${item.osType}`,
                            filterDataMap
                        );
                        const filter = formatFilterData(filterData);
                        // 获取当前结点的caseNodeId
                        const caseRootId = nodeCaseListMap[item.treeNodeId]?.caseRootId;
                        item.filter = {
                            caseRootId: caseRootId,
                            ...filter
                        };
                    }
                }
            }
            setCreateLoading(true);
            if (query?.planId) {
                await updatePlan({
                    planId: query?.planId,
                    name: commonConfigFormValues.name,
                    treeNodeIdList: treeNodeIdList
                });
                planId = query?.planId;
            } else if (!query?.planId) {
                let params = {
                    moduleId: currentSpace?.id,
                    name: commonConfigFormValues.name,
                    treeNodeIdList: treeNodeIdList
                };
                const androidConfigFormValues = androidConfigForm.getFieldsValue();
                const iosConfigFormValues = iosConfigForm.getFieldsValue();
                const serverConfigFormValues = serverConfigForm.getFieldsValue();
                let cloudParams = [];
                // 若有自动化配置
                if (androidConfigFormValues.deviceType !== 2) {
                    let androidParmas = {
                        type: 1,
                        retryTimes: androidConfigFormValues.retryTimes ?? 0,
                        sysAlertClear: androidConfigFormValues.sysAlertClear ?? true,
                        logCollect: {
                            needLog: androidConfigFormValues.logCollect ?? false,
                            filter: androidConfigFormValues.logCollect
                                ? androidConfigFormValues.filter
                                : ''
                        },
                        requestDirectMap: getNewIpRedirect(
                            createPlanOption?.executeConfig || [],
                            'android'
                        ).filter((item) => {
                            return (
                                item.oriAddress.hostname !== undefined &&
                                item.targetAddress.hostname !== undefined
                            );
                        }),
                        envParams: {
                            envDetail: {
                                envId: createPlanOption?.executeConfig?.android?.envParams?.envId,
                                paramList:
                                    createPlanOption?.executeConfig?.android?.envParams?.paramList?.map(
                                        (item) => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue
                                        })
                                    ),
                                appList:
                                    createPlanOption?.executeConfig?.android?.envParams?.appList?.map(
                                        (item) => ({
                                            appId: item.appId,
                                            envValue: item.envValue
                                        })
                                    ),
                                serverList:
                                    createPlanOption?.executeConfig?.android?.envParams?.serverList?.map(
                                        (item) => ({
                                            serverId: item.serverId,
                                            envValue: item.envValue
                                        })
                                    )
                            }
                        },
                        alarmInfo: {
                            toid: +androidConfigFormValues?.toid, // 群号
                            webhook: androidConfigFormValues?.webhook, // 机器人地址
                            atuseridName: androidConfigFormValues?.atuseridName, // 需@的人
                            statusList: androidConfigFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                        }
                    };
                    // 仅本地设备参数 打点自动化校验
                    if (androidConfigFormValues.deviceType === 3) {
                        androidParmas.taskTimeout = androidConfigFormValues.taskTimeout;
                        androidParmas.deviceIdList = androidConfigFormValues.localDevice;
                        androidParmas.poolList = [0];
                        androidParmas.logcheckInfo = {
                            needLogCheck: androidConfigFormValues.logcheckInfo ?? false,
                            cuid: androidConfigFormValues.logcheckInfo
                                ? androidConfigFormValues.cuid
                                : ''
                        };
                    }
                    // 仅云端设备参数 安装app
                    if (androidConfigFormValues.deviceType === 1) {
                        androidParmas.poolList = androidConfigFormValues.cloudDevice;
                        androidParmas.installParams = androidConfigFormValues?.installParams ?? [];
                    }
                    cloudParams.push(androidParmas);
                }
                if (iosConfigFormValues.deviceType !== 2) {
                    let iosParmas = {
                        type: 2,
                        retryTimes: iosConfigFormValues.retryTimes ?? 0,
                        sysAlertClear: iosConfigFormValues.sysAlertClear ?? true,
                        requestDirectMap: getNewIpRedirect(
                            createPlanOption?.executeConfig || [],
                            'ios'
                        ).filter((item) => {
                            return (
                                item.oriAddress.hostname !== undefined &&
                                item.targetAddress.hostname !== undefined
                            );
                        }),
                        envParams: {
                            envDetail: {
                                envId: createPlanOption?.executeConfig?.ios?.envParams?.envId,
                                paramList:
                                    createPlanOption?.executeConfig?.ios?.envParams?.paramList?.map(
                                        (item) => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue
                                        })
                                    ),
                                appList:
                                    createPlanOption?.executeConfig?.ios?.envParams?.appList?.map(
                                        (item) => ({
                                            appId: item.appId,
                                            envValue: item.envValue
                                        })
                                    ),
                                serverList:
                                    createPlanOption?.executeConfig?.ios?.envParams?.serverList?.map(
                                        (item) => ({
                                            serverId: item.serverId,
                                            envValue: item.envValue
                                        })
                                    )
                            }
                        },
                        alarmInfo: {
                            toid: +iosConfigFormValues?.toid, // 群号
                            webhook: iosConfigFormValues?.webhook, // 机器人地址
                            atuseridName: iosConfigFormValues?.atuseridName, // 需@的人
                            statusList: iosConfigFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                        }
                    };
                    // 仅本地设备参数 打点自动化校验
                    if (iosConfigFormValues.deviceType === 3) {
                        iosParmas.deviceIdList = iosConfigFormValues.localDevice;
                        iosParmas.taskTimeout = iosConfigFormValues.taskTimeout;
                        iosParmas.poolList = [0];
                        iosParmas.logcheckInfo = {
                            needLogCheck: iosConfigFormValues.logcheckInfo ?? false,
                            cuid: iosConfigFormValues.logcheckInfo ? iosConfigFormValues.cuid : ''
                        };
                        if (iosConfigFormValues.localDevice.length === 0) {
                            messageApi.error('设备获取失败，请重新选择');
                            return;
                        }
                    }
                    // 仅云端设备参数 安装app
                    if (iosConfigFormValues.deviceType === 1) {
                        iosParmas.poolList = iosConfigFormValues.cloudDevice;
                        iosParmas.installParams = iosConfigFormValues?.installParams ?? [];
                        if (iosConfigFormValues.cloudDevice.length === 0) {
                            messageApi.error('设备池获取失败，请重新选择');
                            return;
                        }
                    }
                    cloudParams.push(iosParmas);
                }
                // server配置
                if (serverConfigFormValues.deviceType === 1) {
                    let serverParmas = {
                        type: 4,
                        modeType: serverConfigFormValues.modeType ?? 1, // 模式类型: int: 1:普通模式（默认）；2:diff模式。
                        modeConfig: {
                            // 模式配置:
                            serverConfig: {
                                //  Server 配置
                                stableEnvironmentId: serverConfigFormValues?.stableEnvironmentId, // 稳定版环境id
                                testEnvironmentId: serverConfigFormValues?.testEnvironmentId // 待测版环境id
                            },
                            assertConfig: {
                                // 断言配置
                                jsonSchemaCheck: serverConfigFormValues.jsonSchemaCheck ?? false, // JSON Schema 校验
                                intelligentNoiseReduce:
                                    serverConfigFormValues.intelligentNoiseReduce ?? false // 智能去噪
                            }
                        },
                        poolList: [serverConfigFormValues.poolId],
                        envParams: {
                            envId: createPlanOption?.executeConfig?.server?.envParams?.envId,
                            envDetail: {
                                paramList:
                                    createPlanOption?.executeConfig?.server?.envParams?.paramList?.map(
                                        (item) => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue
                                        })
                                    ),
                                appList:
                                    createPlanOption?.executeConfig?.server?.envParams?.appList?.map(
                                        (item) => ({
                                            appId: item.appId,
                                            envValue: item.envValue
                                        })
                                    ),
                                serverList:
                                    createPlanOption?.executeConfig?.server?.envParams?.serverList?.map(
                                        (item) => ({
                                            serverId: item.serverId,
                                            envValue: item.envValue
                                        })
                                    )
                            }
                        },
                        // 报警
                        alarmInfo: {
                            toid: serverConfigFormValues?.toid ? +serverConfigFormValues?.toid : 0, // 群号
                            webhook: serverConfigFormValues?.webhook, // 机器人地址
                            atuseridName: serverConfigFormValues?.atuseridName, // 需@的人
                            statusList: serverConfigFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                        }
                    };
                    cloudParams.push(serverParmas);
                }
                // cloudParams.push(serverConfigFormValues);
                if (!isEmpty(cloudParams)) {
                    params.cloudParams = cloudParams;
                }
                params.planType = query?.planType ?? 1;
                // 自定义创建计划
                // 定时巡检 多了个巡检配置
                // if (+query?.planType === 4) {
                //     params.cron = commonConfigFormValues.cron;
                //     // 创建定时巡检任务
                //     await createCheckTask(params);
                // } else if ('custom' === creationMethod) {
                // 之前的逻辑
                let res = await createPlan(params);
                planId = res?.planId;
                // }
            }
            let { planList } = await getPlanList({
                moduleId: currentSpace?.id,
                pageSize: parseInt((window.innerHeight - 110) / 30, 10),
                planType: currentPlanGroup?.planType
            });

            let planInfo = await getPlanDetail({ planIdList: [+planId] });
            setPlanList(
                planList?.map((item) => {
                    if (item?.id === +planId) {
                        return {
                            ...planInfo?.planList?.[0]
                        };
                    } else {
                        return item;
                    }
                })
            );
            setCurrentPlan(planInfo?.planList?.[0]);
            setCreateLoading(false);
            navigate(
                stringifyUrl({
                    url: '/' + currentModule + '/index',
                    query: {
                        moduleId: currentSpace?.id,
                        planType: query?.planType ?? 1,
                        planId: planId,
                        'filters[planId]': query?.['filters[planId]']
                    }
                })
            );
        } catch (err) {
            console.log(err?.message ?? err, '创建计划失败');
            setCreateLoading(false);
        }
    };

    const onExpand = (newExpandedKeys) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };

    //递归过滤隐藏菜单
    const getTreeNodeIdList = (tree = [], nodeList, nodeListWithOs, arr = []) => {
        if (!tree.length) {
            return [];
        }
        for (let item of tree) {
            // 若无子节点且不在list中，则不显示
            if (item.nodeType === 1) {
                getTreeNodeIdList(item?.children, nodeList, nodeListWithOs, arr);
            } else if (!nodeList.includes(item.nodeId)) {
                continue;
            } else if (nodeList.includes(item.nodeId)) {
                console.log('item', item);
                [1, 2, 3, 4, 5, 6].forEach((os) => {
                    if (nodeListWithOs.includes(item.nodeId + '-' + os)) {
                        if (item?.signType === 1) {
                            arr.push({
                                treeNodeId: item?.nodeId,
                                osType: 3
                            });
                        } else {
                            arr.push({
                                treeNodeId: item?.nodeId,
                                osType: os
                            });
                        }
                    }
                });
            }
        }
        console.log('测试', arr);
        return arr;
    };

    // 获取云端设备池列表
    const obtainCloudDeviceList = async () => {
        try {
            if (!currentSpace?.id) {
                return;
            }
            let androidRes = await getDevicePoolList({
                moduleId: currentSpace?.id,
                poolType: 1
            });
            let iosRes = await getDevicePoolList({
                moduleId: currentSpace?.id,
                poolType: 2
            });
            setCloudDeviceList({
                android: androidRes?.poolList,
                ios: iosRes?.poolList
            });
        } catch (err) {
            message.error(err?.message ?? err);
        }
    };

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        try {
            if (!currentSpace?.id) {
                return;
            }
            let androidRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 1
            });
            let iosRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 2
            });
            let serverRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 4
            });
            setEnvList({
                android: androidRes.envList,
                ios: iosRes.envList,
                server: serverRes?.envList
            });
        } catch (err) {
            message.error(err?.message ?? err);
        }
    };

    const getSelectedCaseNodeList = (planDetail) => {
        const caseNodeList = [];
        const getNodeList = (nodeList) => {
            nodeList?.forEach((item) => {
                // nodeType 为2的节点
                if (item?.nodeType === 2) {
                    // 遍历 caseNodeList 选中的节点
                    item?.caseNodeList?.forEach((node) => {
                        caseNodeList.push({
                            nodeId: item.nodeId,
                            osType: node?.osType,
                            caseIdList: node.caseNodeIdList,
                            caseRootId: item.caseRootId,
                            filter: item.filter
                        });
                    });
                } else {
                    // 目录
                    getNodeList(item.children);
                }
            });
        };
        getNodeList(planDetail?.nodeTree);
        return caseNodeList;
    };
    const getLeafNodeCaseIds = (tree) => {
        let leafIds = [];
        if (isEmpty(tree.children)) {
            leafIds.push(tree.caseNodeId);
        } else {
            for (let node of tree.children) {
                leafIds.push(...getLeafNodeCaseIds(node));
            }
        }
        return leafIds;
    };

    const formatFilterData = (filterData) => {
        const getValue = (field) => {
            return filterData[field].data.map((v) => `${v}`);
        };
        const filter = {
            isAccess: {
                values: getValue('access') // 是否准入 0-非准入 1-准入
            },
            priority: {
                // 节点优先级
                values: getValue('priority')
            },
            tagInfo: {
                values: getValue('tag')
            },
            executionType: {
                // 节点类型, 1-M(人工) 2-A(自动) 4:R（半自动
                values: getValue('executeType')
            },
            record: {
                // 录制信息
                values: getValue('record') // 0-未录制 1-已录制
            }
        };
        return filter;
    };

    const updateSelectKeys = (filterNode, newFilterDataMap) => {
        // 更新当前选择的节点
        const caseNodeList = getLeafNodeIds([filterNode]);
        caseNodeList
            .filter((v) => typeof v === 'string' && v.includes('-'))
            .forEach((v) => {
                const [nodeId, osType] = v.split('-');
                const { filterData } = getFilterData(nodeId, newFilterDataMap);
                const filter = formatFilterData(filterData);
                // 获取当前结点的caseNodeId
                const caseRootId = nodeCaseListMap[nodeId]?.caseRootId;
                setCaseNodeList((list) => {
                    const index = list.findIndex(
                        (v) => v.nodeId === +nodeId && v.osType === +osType
                    );
                    if (index !== -1) {
                        list[index].filter = { caseRootId: caseRootId, ...filter };
                    } else {
                        list.push({
                            nodeId: +nodeId,
                            caseRootId,
                            osType: +osType,
                            caseIdList: [],
                            filter: {
                                caseRootId: caseRootId,
                                ...filter
                            }
                        });
                    }
                    return [...list];
                });
            });
    };
    const getFilterComponent = (node) => {
        const nodeId = node.nodeId;

        return (
            <span style={{ fontSize: 12, color: '#448ef7' }}>
                {/* {nodeId} */}
                <Filter
                    type="check"
                    title="默认勾选"
                    caseNode={[]}
                    filterData={{ ...getFilterData(nodeId, filterDataMap)?.filterData }}
                    handleFilterData={(value, type, activeKey) => {
                        const originFilterData = getFilterData(nodeId, filterDataMap).filterData;
                        let newFilterData = {
                            ...originFilterData,
                            [type]: {
                                activeKey: 'belong',
                                data: value
                            }
                        };
                        const newFilterDataMap = {
                            ...filterDataMap,
                            [nodeId]: newFilterData
                        };
                        setFilterDataMap(newFilterDataMap);
                        setFilterDataIsChangeMap({
                            ...filterDataIsChangeMap,
                            [nodeId]: true
                        });
                        updateSelectKeys(node, newFilterDataMap);
                    }}
                    hiddenNoBelong
                    clearFilterData={(data) => {
                        const newFilterDataMap = {
                            ...filterDataMap,
                            [nodeId]: data
                        };
                        setFilterDataMap(newFilterDataMap);
                        updateSelectKeys(node, newFilterDataMap);
                    }}
                >
                    <Tooltip title="快速勾选用例" placement="right">
                        <span
                            style={{
                                paddingLeft: 10
                            }}
                        >
                            <Badge
                                dot={Object.values(getFilterData(nodeId)?.filterData || {}).some(
                                    (value) => !isEmpty(value?.data)
                                )} // 一个条件为非空.true
                                size="small"
                            >
                                <FilterOutlined className={styles.filterIcon} />
                            </Badge>
                        </span>
                    </Tooltip>
                </Filter>
            </span>
        );
    };
    // 递归过滤隐藏菜单
    const treeData = useMemo(() => {
        const loop = (data) => {
            // console.log('data',data);
            let newData = [];
            // 检查是否有 osType 不为 4 的子节点
            const hasNonOsType4Child = (node) => {
                if (node.nodeType === 2) {
                    return node.children?.some((child) => child.os !== 4);
                }
                return node.children?.some(hasNonOsType4Child);
            };
            data.forEach((item) => {
                const strTitle = !item?.nodeName || item?.nodeName === null ? '' : item.nodeName;
                const index = strTitle.indexOf(searchValue);
                const beforeStr = strTitle.substring(0, index);
                const afterStr = strTitle.slice(index + searchValue.length);
                const title =
                    index > -1 ? (
                        <span>
                            {beforeStr}
                            <span className={styles.highlight}>{searchValue}</span>
                            {afterStr}
                        </span>
                    ) : (
                        <span>{strTitle}</span>
                    );
                if (item?.nodeType === 1) {
                    // 只有当节点自身或其子节点中有 sever端 时才显示
                    const children = loop(item.children);
                    if (children.length > 0 || hasNonOsType4Child(item)) {
                        newData.push({
                            title: (
                                <>
                                    <NodeIcon type={item.nodeType} />
                                    {title}
                                    {/* {item.nodeId} */}
                                    {selectNodeOrhalfCheckedKeys.includes(item.nodeId) &&
                                        getFilterComponent(item)}
                                </>
                            ),
                            key: item.nodeId,
                            value: item.nodeId,
                            children
                        });
                    }
                } else if (item?.nodeType === 2) {
                    const children = loop(item.children);
                    if (children.length > 0 || hasNonOsType4Child(item)) {
                        newData.push({
                            title: (
                                <>
                                    <NodeIcon type={item.nodeType} />
                                    {title}
                                    {/* {item.nodeId} */}
                                    {selectNodeOrhalfCheckedKeys.includes(item.nodeId) && (
                                        <>{getFilterComponent(item)}</>
                                    )}
                                </>
                            ),
                            key: item.nodeId,
                            value: item.nodeId,
                            children
                        });
                    }
                } else if (item.os && item.os !== 4) {
                    let curNodeList =
                        caseNodeList?.find((list) => {
                            let nodeId = item?.nodeId?.split('-')[0];
                            return +list?.nodeId === +nodeId && +item?.os === +list?.osType;
                        })?.caseIdList ?? [];
                    newData.push({
                        title: (
                            <>
                                <RenderTitle
                                    os={item.signType === 1 && item.os !== 3 ? 7 : item.os}
                                />
                                {/* {item.nodeId} */}
                                {selectedNode.includes(item.nodeId) && (
                                    <Tooltip title="筛选需要创建的用例, 默认全选" placement="right">
                                        <Badge dot={curNodeList?.length > 0}>
                                            <FunnelPlotOutlined
                                                className={styles.filter}
                                                onClick={() => {
                                                    let filterData = {};
                                                    // 是否改变
                                                    if (!nodeCaseIsChangeMap[item.nodeId]) {
                                                        // 没有操作过，通过过滤条件筛选
                                                        filterData =
                                                            getFilterData(item.nodeId)
                                                                ?.filterData || {};
                                                    }
                                                    caseNodeTreeModalRef?.current?.show(
                                                        item.caseRootId,
                                                        item.os,
                                                        +item.nodeId.split('-')?.[0],
                                                        caseNodeList,
                                                        filterData
                                                    );
                                                }}
                                            />
                                        </Badge>
                                    </Tooltip>
                                )}
                                {!nodeCaseIsChangeMap[item.nodeId] &&
                                    selectNodeOrhalfCheckedKeys.includes(item.nodeId) &&
                                    getFilterData(item.nodeId, null, true)?.filterTagList?.map(
                                        (name) => {
                                            return (
                                                <Tag className={styles.typeTag} key={name}>
                                                    {name}
                                                </Tag>
                                            );
                                        }
                                    )}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId
                    });
                }
            });
            return newData;
        };
        return loop(directoryTreeData);
    }, [
        searchValue,
        directoryTreeData,
        selectedNode,
        caseNodeList,
        filterDataMap,
        nodeCaseListMap,
        nodeCaseIsChangeMap,
        filterDataIsChangeMap,
        selectNodeOrhalfCheckedKeys
    ]);
    // 新增校验函数
    const checkCaseType = (node, filterType) => {
        if (!filterType || node.nodeType !== 2) {
            return true;
        }
        return filterType === 'manual'
            ? node.caseType === 0
            : filterType === 'auto'
            ? node.caseType === 1
            : true;
    };
    // 获取父级筛选类型
    const getParentFilterType = (node) => {
        let current = node;
        while (current.parentId) {
            current = directoryTreeData.find((n) => n.nodeId === current.parentId);
            if (current?.filterType) {
                return current.filterType;
            }
        }
        return null;
    };
    const onSearchValueChange = (e) => {
        const { value } = e.target;
        const newExpandedKeys = nodeTreeMapList
            .map((item) => {
                if (`${item?.nodeName || ''}`.includes(value)) {
                    return item?.nodeId || null;
                }
                return null;
            })
            .filter((item) => item !== null);
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(true);
        setSearchValue(value);
    };
    // console.log(treeData);
    const executeConfigItems = [
        {
            key: 'android',
            label: '安卓配置',
            icon: <AndroidOutlined />
        },
        {
            key: 'ios',
            label: 'iOS 配置',
            icon: <AppleOutlined />
        }
        // {
        //     key: 'server',
        //     label: '服务端配置',
        //     icon: <CloudServerOutlined />
        // }
    ];

    // const planTypeOptions = [
    //     {
    //         value: 1,
    //         label: '源于集成回归用例组'
    //     },
    //     {
    //         value: 2,
    //         disabled: +query?.planType === 5,
    //         label: '源于本地执行用例组'
    //     }
    // ];

    return (
        <Spin spinning={createLoading}>
            <CardContent>
                {contextHolder}
                <Descriptions column={1} size="small" />
                <div className={styles.header}>
                    <CardHeader
                        text={query?.planId ? '更新配置' : '新建计划'}
                        extra={
                            <>
                                {query?.planId ? (
                                    <Popconfirm
                                        placement="right"
                                        title="您确定要更新测试计划的配置？"
                                        description="特别注意: 若取消勾选的用例将清除所有执行数据"
                                        onConfirm={onClick}
                                        cancelText="取消"
                                        okText="确认"
                                    >
                                        <Button type="primary">更新</Button>
                                    </Popconfirm>
                                ) : (
                                    <Button type="primary" onClick={onClick}>
                                        创建
                                    </Button>
                                )}
                                <Button
                                    onClick={() => {
                                        navigate(-1);
                                    }}
                                >
                                    取消
                                </Button>
                            </>
                        }
                    />
                </div>
                <div className={styles.container}>
                    <Form
                        form={commonConfigForm}
                        layout="vertical"
                        requiredMark={false}
                        colon={false}
                    >
                        <CardTitle text="计划名称" />
                        <div className={styles.cardLayout}>
                            <Form.Item
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: '请输入测试计划名称'
                                    }
                                ]}
                            >
                                <Input placeholder="请输入内容" allowClear />
                            </Form.Item>
                        </div>
                        <CardTitle text="测试用例">
                            <Select
                                variant="borderless"
                                style={{
                                    width: +query?.planType !== 5 ? 161 : 145
                                }}
                                className={styles.selecPlanType}
                                popupMatchSelectWidth={false}
                                size="small"
                                value={groupId}
                                suffixIcon={+query?.planType !== 5 ? <DownOutlined /> : null}
                                options={planTypeOptions(filteredList)}
                                onChange={(value) => {
                                    // setPlanType(value);
                                    setGroupId(value);
                                    getTreeNodeListWithPlanType(value, groupList);
                                    // 本地要切换设备
                                    if (query?.planType === 2) {
                                        if (androidConfigForm.getFieldValue('deviceType') === 1) {
                                            androidConfigForm.setFieldValue(
                                                'deviceType',
                                                isElectron() ? 3 : 2
                                            );
                                        }
                                        if (iosConfigForm.getFieldValue('deviceType') === 1) {
                                            iosConfigForm.setFieldValue(
                                                'deviceType',
                                                isElectron() ? 3 : 2
                                            );
                                        }
                                    }
                                }}
                            />
                            {/* 如果是更新配置，不支持从模板导入 */}
                            {!query?.planId && (
                                <Popconfirm
                                    placement="right"
                                    title={'该操作会覆盖当前已选择的数据，请谨慎操作'}
                                    // width={300}
                                    // disabled
                                    destroyTooltipOnHide
                                    // zIndexPopup={1}
                                    style={{ width: 800 }}
                                    description={
                                        <SelectWithDropdownRender
                                            style={{
                                                flex: 1,
                                                margin: '10px 0',
                                                width: 300
                                            }}
                                            placeholder="请选择模板" // 占位符文本
                                            text="计划模板选择" // 用于显示的文本
                                            disabled={query?.planId} // 根据需要启用或禁用选择
                                            showSearchIcon={false} // 显示搜索图标
                                            // 选项列表 只展示当前planType下用例组对应的模板
                                            options={templateList
                                                .filter((item) =>
                                                    filteredList.some(
                                                        (filterItem) =>
                                                            filterItem.groupId === item.groupId
                                                    )
                                                )
                                                .map((item) => ({
                                                    value: item.templateId,
                                                    label: item.templateName
                                                }))}
                                            filterOption={(input, option) =>
                                                option.label.includes(input)
                                            }
                                            showSearch
                                            value={currentTemplateId} // 当前选中的服务ID
                                            onChange={(value) => {
                                                // 当选中的服务变化时，执行的操作
                                                setCurrentTemplateId(value);
                                            }}
                                            onClick={() => {
                                                fetchTemplates();
                                            }}
                                            addChange={() => {
                                                creatTemplateModalRef.current.show();
                                            }}
                                            settingChange={() => {
                                                templateManagerRef.current?.show({
                                                    key: 'plan-template',
                                                    label: '计划模板',
                                                    path: '/system-setting/plan-template'
                                                });
                                            }}
                                            allowClear
                                        />
                                    }
                                    okText="确定"
                                    cancelText="取消"
                                    onConfirm={async () => {
                                        try {
                                            // 清空当前的表单数据;
                                            commonConfigForm.resetFields();
                                            androidConfigForm.resetFields();
                                            iosConfigForm.resetFields();
                                            serverConfigForm.resetFields();
                                            const currentTemplateDetail =
                                                await getPlanTemplateDetail({
                                                    templateId: currentTemplateId
                                                });
                                            setGroupId(currentTemplateDetail?.groupId);
                                            getTreeNodeListWithPlanType(
                                                currentTemplateDetail?.groupId,
                                                groupList
                                            );
                                            const templateConfig =
                                                currentTemplateDetail.templateConfig;
                                            const selectedNodes = templateConfig.treeNodeIdList.map(
                                                (node) => `${node.treeNodeId}-${node.osType}`
                                            );
                                            // 设置计划名称
                                            commonConfigForm.setFieldsValue({
                                                name: templateConfig.name,
                                                nodeTree: selectedNodes
                                            });
                                            setSelectedNode(selectedNodes);

                                            // 设置自动化配置
                                            templateConfig?.cloudParams?.forEach((param) => {
                                                if (param.type === 1) {
                                                    // Android
                                                    androidConfigForm.setFieldsValue({
                                                        retryTimes: param?.retryTimes,
                                                        deviceType:
                                                            param?.deviceIdList?.length > 0
                                                                ? 3
                                                                : param?.poolList?.length > 0
                                                                ? 1
                                                                : 2,
                                                        localDevice: param?.deviceIdList,
                                                        cloudDevice: param?.poolList,
                                                        sysAlertClear: param?.sysAlertClear,
                                                        logCollect: param?.logCollect?.needLog,
                                                        filter: param?.logCollect?.filter,
                                                        taskTimeout: param?.taskTimeout ?? 20,
                                                        logcheckInfo:
                                                            param?.logcheckInfo?.needLogCheck,
                                                        cuid: param?.logcheckInfo?.cuid,
                                                        installParams: param?.installParams,
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        atuseridName: param?.alarmInfo?.atuseridName
                                                    });
                                                    if (param?.alarmInfo?.toid) {
                                                        setAndroidShowMore(true);
                                                    } else {
                                                        setAndroidShowMore(false);
                                                    }
                                                } else if (param.type === 2) {
                                                    // iOS
                                                    iosConfigForm.setFieldsValue({
                                                        retryTimes: param.retryTimes,
                                                        deviceType:
                                                            param?.deviceIdList?.length > 0
                                                                ? 3
                                                                : param?.poolList?.length > 0
                                                                ? 1
                                                                : 2,
                                                        localDevice: param?.deviceIdList,
                                                        cloudDevice: param?.poolList,
                                                        sysAlertClear: param?.sysAlertClear,
                                                        logCollect: param?.logCollect?.needLog,
                                                        filter: param?.logCollect?.filter,
                                                        taskTimeout: param?.taskTimeout ?? 20,
                                                        logcheckInfo:
                                                            param?.logcheckInfo?.needLogCheck,
                                                        cuid: param?.logcheckInfo?.cuid,
                                                        installParams: param?.installParams,
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        atuseridName: param?.alarmInfo?.atuseridName
                                                    });
                                                    if (param?.alarmInfo?.toid) {
                                                        setIosShowMore(true);
                                                    } else {
                                                        setIosShowMore(false);
                                                    }
                                                } else if (param.type === 4) {
                                                    // Server
                                                    serverConfigForm.setFieldsValue({
                                                        poolId: param?.poolList[0], // 只取第一个执行器集群
                                                        envParams: param?.envParams, // 根据需要调整结构
                                                        toid: param?.alarmInfo?.toid
                                                            ? +param?.alarmInfo?.toid
                                                            : null,
                                                        webhook: param?.alarmInfo?.webhook,
                                                        statusList: param?.alarmInfo?.statusList,
                                                        deviceType:
                                                            param?.poolList?.length > 0 ? 1 : 2,
                                                        atuseridName:
                                                            param?.alarmInfo?.atuseridName,
                                                        modeType: param?.modeType,
                                                        stableEnvironmentId:
                                                            param?.modeConfig?.serverConfig
                                                                ?.stableEnvironmentId,
                                                        testEnvironmentId:
                                                            param?.modeConfig?.serverConfig
                                                                ?.testEnvironmentId,
                                                        jsonSchemaCheck:
                                                            param?.modeConfig?.assertConfig
                                                                ?.jsonSchemaCheck,
                                                        intelligentNoiseReduce:
                                                            param?.modeConfig?.assertConfig
                                                                ?.intelligentNoiseReduce
                                                    });
                                                    if (param?.alarmInfo?.toid) {
                                                        setServerShowMore(true);
                                                    } else {
                                                        setServerShowMore(false);
                                                    }
                                                }
                                            });

                                            // 更新状态
                                            setCreatePlanOption((prev) => ({
                                                ...prev,
                                                executeConfig: {
                                                    android: {
                                                        ...prev.executeConfig?.android,
                                                        envParams:
                                                            templateConfig?.cloudParams?.find(
                                                                (p) => p.type === 1
                                                            )?.envParams || {},
                                                        ipRedirect:
                                                            getNewIpRedirectReverse(
                                                                templateConfig?.cloudParams?.find(
                                                                    (p) => p.type === 1
                                                                )?.requestDirectMap
                                                            ) || []
                                                    },
                                                    ios: {
                                                        ...prev.executeConfig.ios,
                                                        envParams:
                                                            templateConfig?.cloudParams?.find(
                                                                (p) => p.type === 2
                                                            )?.envParams || {},
                                                        ipRedirect:
                                                            getNewIpRedirectReverse(
                                                                templateConfig?.cloudParams?.find(
                                                                    (p) => p.type === 2
                                                                )?.requestDirectMap
                                                            ) || []
                                                    },
                                                    server: {
                                                        ...prev.executeConfig.server,
                                                        envParams:
                                                            templateConfig?.cloudParams?.find(
                                                                (p) => p.type === 4
                                                            )?.envParams || {}
                                                    }
                                                }
                                            }));
                                            setCaseNodeList(
                                                templateConfig?.treeNodeIdList?.map((item) => {
                                                    return {
                                                        ...item,
                                                        nodeId: item.treeNodeId,
                                                        caseIdList: item.caseNodeList
                                                    };
                                                })
                                            );
                                        } catch (error) {
                                            console.error(error);
                                            messageApi.error('加载模板失败，请重试');
                                        }
                                    }}
                                >
                                    <Button type="link">从模板快速导入配置</Button>
                                </Popconfirm>
                            )}
                        </CardTitle>
                        <div className={styles.cardLayout}>
                            <Form.Item
                                name="nodeTree"
                                shouldUpdate={() => false}
                                required
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (selectedNode && selectedNode.length > 0) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject(
                                                new Error('请选择需要测试的用例')
                                            );
                                        }
                                    })
                                ]}
                            >
                                <div className={styles.nodeTree}>
                                    <Search
                                        className={styles.search}
                                        value={searchValue}
                                        placeholder="请输入要检索的内容"
                                        onChange={(e) => onSearchValueChange(e)}
                                    />
                                    <Spin spinning={loading}>
                                        <div
                                            className={styles.tree}
                                            style={{ maxHeight: window.innerHeight - 400 }}
                                        >
                                            {treeData?.length ? (
                                                <Tree
                                                    onExpand={onExpand}
                                                    checkable
                                                    showLine
                                                    autoExpandParent={autoExpandParent}
                                                    expandedKeys={expandedKeys}
                                                    treeData={treeData}
                                                    checkedKeys={selectedNode}
                                                    onCheck={(
                                                        keys,
                                                        { checkedNodes, halfCheckedKeys }
                                                    ) => {
                                                        const selectKes = [
                                                            ...checkedNodes.map((item) => item.key)
                                                        ];
                                                        setSelectNodeOrhalfCheckedKeys([
                                                            ...selectKes,
                                                            ...halfCheckedKeys
                                                        ]);
                                                        setSelectedNode(selectKes);
                                                        let newCaseNodeList = [];
                                                        keys = keys.filter((item) =>
                                                            (item + '').includes('-')
                                                        );
                                                        for (let item of caseNodeList) {
                                                            if (
                                                                keys.includes(
                                                                    item.nodeId + '-' + item.osType
                                                                )
                                                            ) {
                                                                newCaseNodeList.push(item);
                                                            }
                                                        }
                                                        setCaseNodeList(newCaseNodeList);
                                                        commonConfigForm.setFieldValue('nodeTree', [
                                                            ...checkedNodes.map((item) => item.key)
                                                        ]);
                                                    }}
                                                />
                                            ) : (
                                                <NoContent
                                                    text="暂无集成回归用例"
                                                    className={styles.noContent}
                                                />
                                            )}
                                        </div>
                                    </Spin>
                                </div>
                            </Form.Item>
                        </div>
                    </Form>
                    {/* 小助手配置 */}
                    {!query?.planId && (
                        <>
                            <CardTitle text="自动化小助手配置" />
                            <div className={styles.cardLayout}>
                                <Tabs
                                    type="card"
                                    items={executeConfigItems}
                                    activeKey={activeExecuteConfigKey}
                                    onChange={setActiveExecuteConfigKey}
                                />
                                <Form
                                    style={{
                                        display:
                                            activeExecuteConfigKey !== 'android' ? 'none' : 'block'
                                    }}
                                    form={androidConfigForm}
                                    layout="vertical"
                                    requiredMark={false}
                                    colon={false}
                                    initialValues={{
                                        statusList: [3, 4]
                                    }}
                                >
                                    {/* 是否使用设备选择 */}
                                    <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                                    <DeviceType
                                        cloudDeviceList={cloudDeviceList?.android ?? []}
                                        form={androidConfigForm}
                                        osType={1}
                                        disabledValueList={selectGroupType === 4 ? [1] : []}
                                        // planType={planType}
                                    />
                                    <Form.Item
                                        noStyle
                                        shouldUpdate={(prevValues, currentValues) =>
                                            prevValues.deviceType !== currentValues.deviceType
                                        }
                                    >
                                        {({ getFieldValue }) => {
                                            return (
                                                <>
                                                    {getFieldValue('deviceType') !== 2 && (
                                                        <>
                                                            {/* 具体设备选择 */}
                                                            {getFieldValue('deviceType') === 1 ? (
                                                                <CloudDevice
                                                                    form={androidConfigForm}
                                                                    cloudDeviceList={
                                                                        cloudDeviceList?.android ??
                                                                        []
                                                                    }
                                                                    osType={1}
                                                                />
                                                            ) : (
                                                                <LocalDevice
                                                                    form={androidConfigForm}
                                                                    osType={1}
                                                                />
                                                            )}

                                                            {/* 执行重试次数 */}
                                                            <RetryTimes form={androidConfigForm} />
                                                            <EnvParams
                                                                form={androidConfigForm}
                                                                osType={1}
                                                                envList={envList?.android ?? []}
                                                                createPlanOption={createPlanOption}
                                                                setCreatePlanOption={
                                                                    setCreatePlanOption
                                                                }
                                                            />
                                                            {/* 是否安装包 */}
                                                            {getFieldValue('deviceType') === 1 && (
                                                                <InstallParams
                                                                    form={androidConfigForm}
                                                                />
                                                            )}

                                                            <Form.Item
                                                                label="其他配置"
                                                                name="otherConfig"
                                                                initialValue={0}
                                                                layout="horizontal"
                                                            >
                                                                <a
                                                                    style={{
                                                                        textDecoration: 'underline'
                                                                    }}
                                                                    onClick={() => {
                                                                        androidConfigForm.setFieldValue(
                                                                            'otherConfig',
                                                                            !showOtherConfig
                                                                        );
                                                                        setShowOtherConfig(
                                                                            !showOtherConfig
                                                                        );
                                                                    }}
                                                                >
                                                                    {showOtherConfig
                                                                        ? '收起'
                                                                        : '展开'}
                                                                </a>
                                                            </Form.Item>
                                                            <div
                                                                style={{
                                                                    display: showOtherConfig
                                                                        ? 'block'
                                                                        : 'none'
                                                                }}
                                                            >
                                                                {/* 是否使用系统弹窗 */}
                                                                <SystemPop
                                                                    form={androidConfigForm}
                                                                />
                                                                {/* 是否打点上报 */}
                                                                {getFieldValue('deviceType') ===
                                                                    3 && <LogCheck />}
                                                                {/* 是否日志收集 */}
                                                                <LogCat />
                                                                {/* 日志转发配置 */}
                                                                <IpRedirect
                                                                    createPlanOption={
                                                                        createPlanOption
                                                                    }
                                                                    setCreatePlanOption={
                                                                        setCreatePlanOption
                                                                    }
                                                                    isExcecuteConfig={true}
                                                                    osType={1}
                                                                />
                                                                {/* 单任务超时时间 */}
                                                                {getFieldValue('deviceType') ===
                                                                    3 && (
                                                                    <TaskTimeout
                                                                        form={androidConfigForm}
                                                                    />
                                                                )}
                                                            </div>
                                                            <NotificationConfig
                                                                showMore={androidShowMore}
                                                                setShowMore={setAndroidShowMore}
                                                            />
                                                        </>
                                                    )}
                                                </>
                                            );
                                        }}
                                    </Form.Item>
                                </Form>
                                <Form
                                    style={{
                                        display: activeExecuteConfigKey !== 'ios' ? 'none' : 'block'
                                    }}
                                    form={iosConfigForm}
                                    layout="vertical"
                                    requiredMark={false}
                                    colon={false}
                                    initialValues={{
                                        statusList: [3, 4]
                                    }}
                                >
                                    <CardTitle text="设备配置" style={{ marginTop: 0 }} />
                                    {/* 是否使用设备选择 */}
                                    <DeviceType
                                        cloudDeviceList={cloudDeviceList?.ios ?? []}
                                        form={iosConfigForm}
                                        osType={2}
                                        disabledValueList={selectGroupType === 4 ? [1] : []}
                                        // planType={planType}
                                    />
                                    <Form.Item
                                        noStyle
                                        shouldUpdate={(prevValues, currentValues) =>
                                            prevValues.deviceType !== currentValues.deviceType
                                        }
                                    >
                                        {({ getFieldValue }) => {
                                            return (
                                                <>
                                                    {getFieldValue('deviceType') !== 2 && (
                                                        <>
                                                            {/* 具体设备选择 */}
                                                            {getFieldValue('deviceType') === 1 ? (
                                                                <CloudDevice
                                                                    form={iosConfigForm}
                                                                    cloudDeviceList={
                                                                        cloudDeviceList?.ios ?? []
                                                                    }
                                                                    osType={2}
                                                                />
                                                            ) : (
                                                                <LocalDevice
                                                                    form={iosConfigForm}
                                                                    osType={2}
                                                                />
                                                            )}
                                                            {/* 执行重试次数 */}
                                                            <RetryTimes form={iosConfigForm} />
                                                            <EnvParams
                                                                form={iosConfigForm}
                                                                osType={2}
                                                                envList={envList?.ios ?? []}
                                                                createPlanOption={createPlanOption}
                                                                setCreatePlanOption={
                                                                    setCreatePlanOption
                                                                }
                                                            />
                                                            {/* 是否安装包 */}
                                                            {getFieldValue('deviceType') === 1 && (
                                                                <InstallParams
                                                                    form={iosConfigForm}
                                                                />
                                                            )}

                                                            <Form.Item
                                                                label="其他配置"
                                                                name="otherConfig"
                                                                initialValue={0}
                                                                layout="horizontal"
                                                            >
                                                                <a
                                                                    style={{
                                                                        textDecoration: 'underline'
                                                                    }}
                                                                    onClick={() => {
                                                                        iosConfigForm.setFieldValue(
                                                                            'otherConfig',
                                                                            !showOtherConfig
                                                                        );
                                                                        setShowOtherConfig(
                                                                            !showOtherConfig
                                                                        );
                                                                    }}
                                                                >
                                                                    {showOtherConfig
                                                                        ? '收起'
                                                                        : '展开'}
                                                                </a>
                                                            </Form.Item>
                                                            <div
                                                                style={{
                                                                    display: showOtherConfig
                                                                        ? 'block'
                                                                        : 'none'
                                                                }}
                                                            >
                                                                {/* 是否使用系统弹窗 */}
                                                                <SystemPop form={iosConfigForm} />
                                                                {/* 是否打点上报 */}
                                                                {getFieldValue('deviceType') ===
                                                                    3 && <LogCheck />}
                                                                {/* 是否日志收集 */}
                                                                <LogCat />
                                                                {/* 日志转发配置 */}
                                                                <IpRedirect
                                                                    createPlanOption={
                                                                        createPlanOption
                                                                    }
                                                                    setCreatePlanOption={
                                                                        setCreatePlanOption
                                                                    }
                                                                    isExcecuteConfig={true}
                                                                    osType={2}
                                                                />
                                                                {/* 单任务超时时间 */}
                                                                {getFieldValue('deviceType') ===
                                                                    3 && (
                                                                    <TaskTimeout
                                                                        form={androidConfigForm}
                                                                    />
                                                                )}
                                                            </div>
                                                            <NotificationConfig
                                                                showMore={iosShowMore}
                                                                setShowMore={setIosShowMore}
                                                            />
                                                        </>
                                                    )}
                                                </>
                                            );
                                        }}
                                    </Form.Item>
                                </Form>
                                {+query.planType === 5 && activeExecuteConfigKey === 'server' ? (
                                    <Empty
                                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        description="精准录制模式下暂不支持该配置"
                                        style={{ marginBottom: '50px' }}
                                    />
                                ) : (
                                    <Form
                                        style={{
                                            display:
                                                activeExecuteConfigKey === 'server'
                                                    ? 'block'
                                                    : 'none'
                                        }}
                                        initialValues={{
                                            statusList: [3, 4]
                                        }}
                                        form={serverConfigForm}
                                        layout="vertical"
                                        requiredMark={false}
                                        colon={false}
                                    >
                                        {/* 是否使用设备选择 */}
                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) =>
                                                prevValues.modeType !== currentValues.modeType ||
                                                prevValues.deviceType !== currentValues.deviceType
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                const modeType = getFieldValue('modeType');
                                                const deviceType = getFieldValue('deviceType');
                                                return (
                                                    <>
                                                        <CardTitle
                                                            text="设备配置"
                                                            style={{ marginTop: 0 }}
                                                        />
                                                        <DeviceType
                                                            cloudDeviceList={poolList ?? []}
                                                            form={serverConfigForm}
                                                            osType={4}
                                                            disabledValueList={
                                                                selectGroupType === 4 ? [1] : []
                                                            }
                                                            // planType={planType}
                                                        />
                                                        {/* <Form.Item
                                                            noStyle
                                                            shouldUpdate={(
                                                                prevValues,
                                                                currentValues
                                                            ) =>
                                                                prevValues.deviceType !==
                                                                currentValues.deviceType
                                                            }
                                                        >
                                                            {({ getFieldValue }) => {
                                                                return (
                                                                    <>
                                                                        {getFieldValue(
                                                                            'deviceType'
                                                                        ) !== 2 && ( */}
                                                        {deviceType !== 2 && (
                                                            <>
                                                                <Form.Item
                                                                    label="执行器集群"
                                                                    name="poolId"
                                                                >
                                                                    <Select
                                                                        placeholder="请选择执行器集群"
                                                                        options={poolList.map(
                                                                            (item) => ({
                                                                                value: item.poolId,
                                                                                label: item.poolName // 显示服务器名称和地址
                                                                            })
                                                                        )}
                                                                        allowClear
                                                                    />
                                                                </Form.Item>
                                                                <CardTitle
                                                                    text="执行模式"
                                                                    style={{ marginTop: 0 }}
                                                                />

                                                                <Form.Item
                                                                    // label="执行模式"
                                                                    name="modeType"
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                '请选择执行模式'
                                                                        }
                                                                    ]}
                                                                    initialValue={0}
                                                                >
                                                                    <Radio.Group>
                                                                        <Radio value={0}>
                                                                            普通模式
                                                                        </Radio>
                                                                        <Radio value={1}>
                                                                            Diff 模式
                                                                        </Radio>
                                                                    </Radio.Group>
                                                                </Form.Item>
                                                                {modeType === 0 ? (
                                                                    <EnvParams
                                                                        form={serverConfigForm}
                                                                        osType={4}
                                                                        envList={
                                                                            envList?.server ?? []
                                                                        }
                                                                        createPlanOption={
                                                                            createPlanOption
                                                                        }
                                                                        setCreatePlanOption={
                                                                            setCreatePlanOption
                                                                        }
                                                                    />
                                                                ) : null}
                                                                {modeType === 1 ? (
                                                                    <>
                                                                        <CardTitle text="Server 配置" />
                                                                        <Form.Item
                                                                            label="稳定版环境"
                                                                            name="stableEnvironmentId"
                                                                        >
                                                                            <Select
                                                                                placeholder="请选择稳定版环境"
                                                                                options={
                                                                                    envList?.server?.map(
                                                                                        (item) => {
                                                                                            return {
                                                                                                label: item.envName,
                                                                                                value: item.envId
                                                                                            };
                                                                                        }
                                                                                    ) ?? []
                                                                                }
                                                                                allowClear
                                                                            />
                                                                        </Form.Item>
                                                                        <Form.Item
                                                                            label="待测版环境"
                                                                            name="testEnvironmentId"
                                                                        >
                                                                            <Select
                                                                                placeholder="请选择待测版环境"
                                                                                options={
                                                                                    envList?.server?.map(
                                                                                        (item) => {
                                                                                            return {
                                                                                                label: item.envName,
                                                                                                value: item.envId
                                                                                            };
                                                                                        }
                                                                                    ) ?? []
                                                                                }
                                                                                allowClear
                                                                            />
                                                                        </Form.Item>
                                                                        <CardTitle text="断言配置" />
                                                                        <Form.Item
                                                                            label="JSON Schema 校验"
                                                                            tooltip="开启后，将使用开启的 JSON Schema 断言对各服务的返回结果做校验"
                                                                            name="jsonSchemaCheck"
                                                                            // layout="line"
                                                                        >
                                                                            <Switch />
                                                                        </Form.Item>
                                                                        <Form.Item
                                                                            label="智能去噪"
                                                                            name="intelligentNoiseReduce"
                                                                            tooltip="开启后，在 Diff 忽略 Key 断言失败时，会智能计算噪声 Key 并回写到断言，再次执行 Diff 忽略 Key 断言校验"
                                                                        >
                                                                            <Switch />
                                                                        </Form.Item>
                                                                    </>
                                                                ) : null}

                                                                <NotificationConfig
                                                                    showMore={serverShowMore}
                                                                    setShowMore={setServerShowMore}
                                                                />
                                                            </>
                                                        )}
                                                        {/* )}
                                                                    </>
                                                                );
                                                            }}
                                                        </Form.Item> */}
                                                    </>
                                                );
                                            }}
                                        </Form.Item>
                                    </Form>
                                )}
                            </div>
                        </>
                    )}
                </div>
                <CaseNodeTree
                    ref={caseNodeTreeModalRef}
                    caseNodeList={caseNodeList}
                    modalType={query?.planId ? 'edit' : 'create'}
                    setCaseNodeList={(e, isInit) => {
                        setCaseNodeList(e);
                        // 非初始化加载节点
                        if (!isInit) {
                            // 判断是否被改过
                            const changeMap = {};
                            e?.forEach((v) => {
                                const id = `${v.nodeId}-${v.osType}`;
                                // 长度不一致且有caseIdList 且不为空数组，则为被修改过
                                const originData = nodeCaseKesMap[id]?.sort((a, b) => a - b);
                                const newData = (v.caseIdList || []).sort((a, b) => a - b);
                                if (
                                    JSON.stringify(originData) !== JSON.stringify(newData) &&
                                    v.caseIdList?.length > 0
                                ) {
                                    changeMap[id] = true;
                                } else {
                                    changeMap[id] = false;
                                }
                            });
                            setNodeCaseIsChangeMap(changeMap);
                        }
                    }}
                    setCaseNodeKeys={(nodeKeys, nodeId) => {
                        // 记录当前节点的所有case叶子结点
                        setNodeCaseKesMap((res) => {
                            return {
                                ...res,
                                [nodeId]: nodeKeys
                            };
                        });
                    }}
                />
            </CardContent>
            <CreatTemplateModal ref={creatTemplateModalRef} />
            <SettingModal ref={templateManagerRef} />
            {/* <TemplateManager ref={templateManagerRef} /> */}
            {/* <SetCenter runCaseSettingRef={runCaseSettingRef} settingModalRef={settingModalRef} />
            <SettingModal ref={settingModalRef} /> */}
        </Spin>
    );
}

export default connectModel([baseModel, planModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule,
    planList: state.common.plan.planList,
    currentPlanGroup: state.common.plan.currentPlanGroup,
    serverList: state.common.case.serverList,
    currentPlan: state.common.plan.currentPlan,
    tagList: state.common.base.tagList,
    username: state.common.base.username
}))(CreateGroupPage);
