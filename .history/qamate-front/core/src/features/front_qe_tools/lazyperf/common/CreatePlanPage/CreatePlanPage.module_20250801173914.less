.noContent {
    margin-top: 5%;
}

.container {
    width: 100%;
    height: 100%;
    position: absolute;
    padding: 20px 70px 20px 0;
    overflow: scroll;
}

.header {
    width: 100%;
    background-color: #fff;
    position: absolute;
    top: 30px;
    left: 0;
    z-index: 9;
    padding: 0 40px 10px 40px;
}

.selecPlanType {
    :global {
        .ant-select-selection-item {
            color: #777 !important;
        }
    }
}

.nodeTree {
    padding-bottom: 10px;
    padding-top: 5px;

    :global {

        .ant-input-status-error,
        .custom-default-input-status-error,
        .custom-dark-input-status-error {
            border: 1px solid var(--border-color) !important;
        }
    }
}

.nodeForm {
    :global {

        .ant-input,
        .custom-default-input,
        .custom-dark-input {
            border: 1px solid var(--border-color) !important;
            background-color: var(--background-color) !important;
        }
    }
}

.tree {
    width: 100%;
    overflow: auto;
    border-radius: 4px;
}

.search {
    color: #1677ff;
}

.operaGroup {
    position: absolute;
    top: 25px;
    left: 125px;
}

.filterOpera {
    float: left;
    margin-right: 5px;
    color: #777;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px;
    border-radius: 4px;
    transition: background-color 0.3s ease;

    &:hover {
        background-color: rgba(0, 0, 0, 0.1);
    }

    .icon {
        color: #777;
    }
}

.filterOperaActived {
    .icon {
        color: #1677ff;
    }
}

.operaInput {
    float: left;
    .input {
        float: left;
        margin-top: -3px;
        width: 200px;
    }
}



.btn {
    margin-right: 5px;
}

.disable {
    display: none !important;
}

.highlight {
    color: red;
}

.typeHighlight {
    background-color: yellow;
}


.cardLayout {
    padding: 0 20px;


    :glbobal {
        .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
            color: var(--primary-color) !important;
        }
    }
}


.defaultValue {
    color: #777;
    font-size: 12px;

}

.optionEditIcon {
    margin-left: 10px;
    color: #777;
    font-size: 12px;
}

.optionEditIcon:hover {
    color: var(--primary-color);
}

.envDetail {
    margin-bottom: 20px;
}

.filter {
    margin-left: 5px;
    font-size: 12px;
    color: #777;
}

.filter:hover {
    color: var(--primary-color);
}

.filter {
    margin-left: 8px;
    color: #1890ff;
    cursor: pointer;
    transition: color 0.3s;
}

.filter:hover {
    color: #40a9ff;
}

.typeTag {
    margin-left: 8px;
    font-size: 12px;
    vertical-align: middle;
}
.filterIcon {
    color:#777777;
}

// 通用布局样式
.flexCenter {
    display: flex;
    align-items: center;
    gap: 8px;
}

.flexCenterMb16 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.flexCenterMb12 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

// 标签样式
.formLabel {
    font-weight: bold;
    min-width: 80px;
    text-align: right;
}

// 对齐的标签样式 - 用于确保录制起始和场景名称对齐
.formLabelAligned {
    font-weight: bold;
    min-width: 80px;
    text-align: right;
    margin-left: 10; // 确保左对齐基准一致
}


.formLabelFullWidth {
    font-weight: bold;
    width: 100%;
}

// 选择器样式
.selectWidth200 {
    width: 200px;
}

.selectWidth161 {
    width: 161px;
}

.selectFlex1 {
    flex: 1;
}

.inputFlex1 {
    flex: 1;
}

// 卡片样式
.cardMb24 {
    margin-bottom: 24px;
}

.cardMb12 {
    margin-bottom: 12px;
    position: relative;
}

// 树形控件样式
.treeContainer {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px;
}

// 按钮样式
.deleteButton {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
}

.addSceneButton {
    border-color: #d9d9d9;
    color: #666666;
    background-color: #fafafa;
    border-radius: 6px;
    height: 40px;
    font-size: 14px;
}

.addSceneButtonIcon {
    margin-right: 4px;
}

// 间距样式
.mb12 {
    margin-bottom: 12px;
}

.mb16 {
    margin-bottom: 16px;
}