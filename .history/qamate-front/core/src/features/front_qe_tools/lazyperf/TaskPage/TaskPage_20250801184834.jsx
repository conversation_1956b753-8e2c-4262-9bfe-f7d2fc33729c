import { useState } from 'react';
import {
    Card,
    Row,
    Col,
    Progress,
    Tag,
    Space,
    Typography,
    Table,
    Spin,
    Button,
    Popover,
    message
} from 'antd';
import { StopOutlined } from '@ant-design/icons';
import { getFormatTime } from 'COMMON/utils/dateUtils';
import AppendPerfPlanModal from 'COMMON/components/NewAddDropdown/AppendPerfPlanModal';
import { cancelPerfPlan } from 'COMMON/api/front_qe_tools/lazyperf';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import styles from './TaskPage.module.less';

const { Text } = Typography;

const TaskPage = (props) => {
    const { curTask, deviceList } = props;
    const [cancelLoading, setCancelLoading] = useState(false);

    const planParams = curTask?.planParams || {};
    const cloudParmas = curTask?.planParams?.cloudParmas || {};
    const roundDetail = curTask?.roundDetail || {};
    // 取消性能评测
    const handleCancelPerfPlan = async () => {
        if (!curTask?.planId) {
            message.error('任务ID不存在');
            return;
        }

        try {
            setCancelLoading(true);
            await cancelPerfPlan({
                planId: curTask.planId
            });
            message.success('取消成功');
            EventBus.emit('refreshPerfPlanList');
        } finally {
            setCancelLoading(false);
        }
    };

    const cancelContent = (
        <div className={styles.cancelContent}>
            <div className={styles.cancelActions}>
                <Space>
                    <Button size="small">取消</Button>
                    <Button
                        type="primary"
                        size="small"
                        loading={cancelLoading}
                        onClick={handleCancelPerfPlan}
                    >
                        确认取消
                    </Button>
                </Space>
            </div>
        </div>
    );

    // 显示加载状态
    if (!curTask) {
        return (
            <div className={styles.taskPage}>
                <div className={styles.loadingContainer}>
                    <Spin size="large" tip="加载任务信息中..." />
                </div>
            </div>
        );
    }

    // 获取状态标签
    const getStatusTag = (status) => {
        const statusMap = {
            0: { text: '待拆分', color: 'default' },
            1: { text: '待执行', color: 'blue' },
            2: { text: '执行中', color: 'processing' },
            3: { text: '执行完成', color: 'success' }
        };
        const { text, color } = statusMap[status] || { text: '未知状态', color: 'default' };
        return <Tag color={color}>{text}</Tag>;
    };

    return (
        <>
            <div className={styles.taskPage}>
                <div className={styles.operateHeader}>
                    <div className={styles.actionButtons}>
                        <AppendPerfPlanModal />
                        <Popover
                            title={`确认取消任务 ${curTask?.planId} 的性能评测吗？`}
                            content={cancelContent}
                            trigger="click"
                            placement="bottomLeft"
                        >
                            <Button
                                shape="round"
                                icon={<StopOutlined />}
                                disabled={!curTask?.planId || curTask?.status === 3} // 已完成的任务不能取消
                            >
                                取消评测
                            </Button>
                        </Popover>
                    </div>
                </div>

                <div className={styles.detailContainer}>
                    <Card
                        title="任务信息"
                        className={styles.taskInfoCard}
                        extra={getStatusTag(curTask.status)}
                    >
                        <Row gutter={[16, 16]}>
                            <Col span={8}>
                                <Space align="start">
                                    <Text strong>ID:</Text>
                                    <Text>{curTask.planId || '-'}</Text>
                                </Space>
                            </Col>
                            <Col span={8}>
                                <Space align="start">
                                    <Text strong>创建时间:</Text>
                                    <Text>
                                        {curTask.createTime
                                            ? getFormatTime(curTask.createTime)
                                            : '-'}
                                    </Text>
                                </Space>
                            </Col>
                            <Col span={8}>
                                <Space align="start">
                                    <Text strong>设备ID:</Text>
                                    <Text>{curTask?.planParams?.cloudParams.deviceId || '-'}</Text>
                                </Space>
                            </Col>
                            <Col span={16}>
                                <Space align="start">
                                    <Text strong>执行状态:</Text>
                                    <div>
                                        <Tag color="blue" className={styles.tagWithMargin}>
                                            {/* 期望 {roundDetail?.totalNum || 0} 次 */}
                                            期望 {curTask?.planParams?.cloudParams?.executeTimes || 0} 次
                                        </Tag>

                                        {(curTask.status === 2 || curTask.status === 3) && (
                                            <>
                                                {(roundDetail?.successNum || 0) > 0 && (
                                                    <Tag
                                                        color="success"
                                                        className={styles.tagWithMargin}
                                                    >
                                                        成功 {roundDetail?.successNum} 次
                                                    </Tag>
                                                )}

                                                {(roundDetail?.failNum || 0) > 0 && (
                                                    <Tag color="error">
                                                        失败 {roundDetail?.failNum} 次
                                                    </Tag>
                                                )}
                                            </>
                                        )}
                                    </div>
                                </Space>
                            </Col>
                        </Row>
                        <Table
                            dataSource={(() => {
                                const caseNodeList = planParams?.caseNodeParams?.caseNodeList || [];
                                const tableData = [];

                                caseNodeList.forEach((caseNode) => {
                                    const sceneList = caseNode.sceneList || [];
                                    sceneList.forEach((scene, sceneIndex) => {
                                        tableData.push({
                                            key: `${caseNode.caseNodeId}-${scene.id}`,
                                            caseName: `用例节点${caseNode.caseNodeId}`,
                                            tti: `${scene.name}: ${1000 + sceneIndex * 200} ms`,
                                            // calibration: {
                                            //     taskStatus: curTask.status,
                                            //     manualCount: scene.manualCount || 0,
                                            //     wholeCount: scene.wholeCount || 10,
                                            //     actualTimes:
                                            //         roundDetail?.totalNum -
                                            //         (roundDetail?.pendingNum || 0), // 总数 - 待执行数 = 已执行数
                                            //     exceptionTimes: roundDetail?.failNum || 0
                                            // }
                                        });
                                    });
                                });

                                return tableData;
                            })()}
                            columns={[
                                {
                                    title: '用例名称',
                                    dataIndex: 'caseName',
                                    key: 'caseName',
                                    align: 'left'
                                },
                                {
                                    title: 'TTI',
                                    dataIndex: 'tti',
                                    key: 'tti',
                                    align: 'left'
                                }
                                // 这期不做
                                // {
                                //     title: '校准进度',
                                //     dataIndex: 'calibration',
                                //     key: 'calibration',
                                //     align: 'left',
                                //     render: (calibrationData) => {
                                //         if (!calibrationData ||
                                //             calibrationData.taskStatus === 0 || // 待拆分
                                //             calibrationData.taskStatus === 1 || // 待执行
                                //             calibrationData.actualTimes === 0 ||
                                //             calibrationData.actualTimes - calibrationData.exceptionTimes === 0) {
                                //             return <span>--</span>;
                                //         }

                                //         // 计算校准进度百分比
                                //         const percent = Math.ceil((calibrationData.manualCount
                                // / calibrationData.wholeCount) * 100);
                                //         return <Progress percent={percent} status={'success'} />;
                                //     }
                                // }
                            ]}
                            pagination={false}
                            bordered={false}
                            size="middle"
                            className={styles.tableWithTopMargin}
                        />
                    </Card>
                </div>
            </div>
        </>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList
}))(TaskPage);
