import { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import { getSpeedRoundList } from 'COMMON/api/front_qe_tools/lazyperf';
import CalibrateTable from './components/CalibrateTable';
import Operate from '../components/Operate';
import styles from './FramePage.module.less';

const FramePage = (props) => {
    const { curTask } = props;
    const [curCaseNodeId, setCurCaseNodeId] = useState(null);
    const [curSceneId, setCurSceneId] = useState(null);
    const [sceneList, setSceneList] = useState([]);
    const [recordList, setRecordList] = useState([]);

    const caseNodeOptions = (curTask?.planParams?.caseNodeParams?.caseNodeList || []).map(
        (item) => ({
            value: item?.caseNodeId,
            key: item?.caseNodeId,
            label: item?.caseNodeName
        })
    );

    const sceneListOptions = (sceneList || []).map((item) => ({
        value: item?.id,
        key: item?.id,
        label: item?.name
    }));

    // 用例和场景初始化逻辑
    // 1. 如果未选中用例，自动选中第一个
    // 2. 更新场景列表并默认选中第一个
    useEffect(() => {
        const caseNodeList = curTask?.planParams?.caseNodeParams?.caseNodeList;

        // 1. 如果未选中用例，自动选中第一个
        if (!isEmpty(caseNodeList) && !curCaseNodeId) {
            const firstCaseNode = caseNodeList[0];
            setCurCaseNodeId(firstCaseNode.caseNodeId);
            return;
        }

        // 2. 更新场景列表并默认选中第一个
        if (curCaseNodeId) {
            const curCaseNode = caseNodeList?.find(
                (item) => item?.caseNodeId === curCaseNodeId
            );
            const newSceneList = curCaseNode?.sceneList || [];
            setSceneList(newSceneList);

            // 默认选中第一个场景
            if (!isEmpty(newSceneList)) {
                const firstSceneId = newSceneList[0].id;
                setCurSceneId(firstSceneId);
            }
        }
    }, [curTask, curCaseNodeId]);

    const refreshRecordList = async () => {
        if (!curCaseNodeId || !curSceneId || !curTask || !curTask.planId) {
            return;
        }

        const params = {
            planId: curTask.planId,
            caseNodeId: curCaseNodeId,
            sceneId: curSceneId
        };

        let res = await getSpeedRoundList(params);

        setRecordList(res?.recordList ?? []);
    };
    useEffect(() => {
        refreshRecordList();
    }, [curTask, curSceneId, curCaseNodeId]);

    return (
        <>
            <div className={styles.taskPage}>
                <Operate
                    planId={curTask?.planId}
                    caseNodeOptions={caseNodeOptions}
                    curCaseNodeId={curCaseNodeId}
                    setCurCaseNodeId={setCurCaseNodeId}
                    curSceneId={curSceneId}
                    setCurSceneId={setCurSceneId}
                    sceneListOptions={sceneListOptions}
                    recordList={recordList}
                    refreshRecordList={refreshRecordList}
                    setRecordList={setRecordList}
                />
                {/* <CalibrateTable recordList={recordList} /> */}
            </div>
        </>
    );
};

export default FramePage;
